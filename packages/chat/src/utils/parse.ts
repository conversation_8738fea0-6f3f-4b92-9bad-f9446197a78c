import { ArtifactChatItem, ArtifactProcessChatItemCodeGen } from '@ks-kwaipilot/utils';
import { BlockType } from '@ks-kwaipilot/utils/src/types/message';

import type {
  CodeSearchResult,
  QuoteSource,
  ThoughtCallProcess,
  ToolCallProcess,
} from '../components/MessageCard/types';
import type { ConversationMessage } from '../types';
import { extractLinksFromCOTReply } from './text';

export type MessageType =
  | 'reply'
  | 'cot_reply'
  | 'WEB_SEARCH'
  | 'warning'
  | 'question'
  | 'images'
  | 'content_reset'
  | 'stay_tool_call_response'
  | 'error'
  | 'workflow'
  | 'ack';

export type WorkflowDebugNodeDetail = {
  moduleType: string;
  moduleName: string;
  textOutput: string;
};

export type RelatedImage = {
  /** 图片链接 */
  url: string;
  /** 图片网页来源 */
  sourceUrl: string;
};

export interface MessageFragment {
  type: FragmentType;
  content: any;
}

export enum FragmentType {
  Markdown,
  Thought,
  Action,
}

export enum ActionType {
  ActionInput,
  Observation,
  Others,
  SearchResult,
}

export interface ActionFragment {
  type: ActionType;
  content: any;
}

export const fragmentSeparators = [
  {
    separator: 'Thought:',
    type: FragmentType.Thought,
  },
  {
    separator: 'Action:',
    type: FragmentType.Action,
  },
  {
    separator: 'Final Answer:',
    type: FragmentType.Markdown,
  },
  {
    separator: 'Answer:',
    type: FragmentType.Markdown,
  },
];

export const actionSeparators = [
  {
    separator: 'Action Input:',
    type: ActionType.ActionInput,
  },
  {
    separator: 'Observation:',
    type: ActionType.Observation,
  },
];

/** 切分COT内容 */
export const split_Fragments = (s: string, isMe: boolean): MessageFragment[] => {
  if (isMe) {
    return [{ type: FragmentType.Markdown, content: s }];
  }
  let result: MessageFragment[] = [{ type: FragmentType.Markdown, content: s }];
  for (const sep of fragmentSeparators) {
    const temp: MessageFragment[] = [];
    for (const substr of result) {
      const splitSubstr = substr.content.split(sep.separator);
      splitSubstr.forEach((item: any, index: number) => {
        if (index == 0 && splitSubstr[index].length === 0) {
          return;
        }
        temp.push({ type: index != 0 ? sep.type : substr.type, content: item });
      });
    }
    result = temp;
  }
  if (result) {
    result.map((item, index) => {
      if (item.type === FragmentType.Action) {
        const actionFragments = split_action_fragments(item.content);
        result[index].content = actionFragments;
      }
    });
  }
  return result;
};

/** 切分Action内容 */
export const split_action_fragments = (s: string): ActionFragment[] => {
  let result: ActionFragment[] = [{ type: ActionType.Others, content: s }];
  for (const sep of actionSeparators) {
    const temp: ActionFragment[] = [];
    for (const substr of result) {
      const splitSubstr = substr.content.split(sep.separator);
      splitSubstr.forEach((item: any, index: number) => {
        if (index == 0 && splitSubstr[index].length === 0) {
          return;
        }
        temp.push({ type: index != 0 ? sep.type : substr.type, content: item });
      });
    }
    result = temp;
  }

  if (result && result[1] && result[2]) {
    if (result[0].type == ActionType.Others && result[2].type == ActionType.Observation) {
      if (result[0].content.trim() == 'Search') {
        const text = result[2].content;
        const regex =
          /(?:^|\n)(\d+)\.\s*(?:【(.*?)】)?(.*?)\s+-\s+snippet:\s+(.*?)\s+-\s+url:\s+(https?:\/\/\S+)(?=\n|$)/g;

        let match;
        const entries: any = [];
        while ((match = regex.exec(text)) !== null) {
          const [, number, title, description, snippet, url] = match;
          const domain = new URL(url)?.hostname || '';
          entries.push({ number, title, description, snippet, url, domain });
        }
        result.push({ type: ActionType.SearchResult, content: entries });
      }
    }
  }
  return result;
};

/** 解析来源附加到消息体上 */
export const appendCOTSourceToMessage = (message: ConversationMessage) => {
  try {
    // 最多使用5个来源
    if (message.sources && message.sources.length >= 5) {
      return;
    }

    const cotLinks = extractLinksFromCOTReply(message.reply.cot);
    cotLinks.forEach((l) => {
      message.sources = message.sources ?? [];
      // 最多使用5个来源
      if (message.sources.length >= 5) {
        return;
      }

      // 如果已经存在，则不再添加
      if (message.sources.find((s) => s.url === l.link)) {
        return;
      }
      message.sources.push({
        title: l.title,
        url: l.link,
      });
    });
    return message;
  } catch (e) {
    console.error('parse cot source error:', e);
  }
};

/** 解析 COT 过程, cb用来埋点 */
export const handleCotMessage = (currentMessage: ConversationMessage, cb?: () => void) => {
  // 至少有10个字符才能解析，避免分隔符未出现的错误情况
  if (currentMessage.reply.cot.length < 10) {
    return currentMessage;
  }

  // gpt有概率会产生没有Thought等思考过程的回答内容，需要补充一个假的
  if (!currentMessage.reply.cot.startsWith('Thought:')) {
    currentMessage.reply.cot = 'Thought: 我会回答。\n Final Answer: ' + currentMessage.reply.cot;
  }

  const segments = split_Fragments(currentMessage.reply.cot, false);
  const calls: (ToolCallProcess | ThoughtCallProcess)[] = [];
  segments.forEach((s, idx) => {
    if (idx === 0 && s.content.trim() === '') {
      return;
    }

    if (s.type === FragmentType.Thought) {
      calls.push({
        type: BlockType.Thought,
        content: s.content.trim(),
      });
      return;
    }

    if (s.type === FragmentType.Markdown) {
      currentMessage.content = s.content;
      cb?.();
      return;
    }

    if (s.type === FragmentType.Action) {
      const call: ToolCallProcess = {
        name: '',
        cot: [],
      };

      (s.content as Array<{ type: ActionType; content: string }>).forEach((a) => {
        if (a.type === ActionType.Others) {
          const action = a.content.trim();
          call.name = action;
          call.cot.push({
            type: BlockType.Action,
            reply: action,
          });
          return;
        }

        if (a.type === ActionType.Observation) {
          call.cot.push({
            type: BlockType.Observation,
            reply: a.content,
          });

          // 尽早解析链接
          appendCOTSourceToMessage(currentMessage);
          return;
        }

        if (a.type === ActionType.ActionInput) {
          call.cot.push({
            type: BlockType.ActionInput,
            reply: a.content,
          });
          return;
        }
      });

      calls.push(call);
    }
  });

  if (calls.length > 0) {
    const process = currentMessage.process || {};
    process.calls = calls;
    currentMessage.process = process;
  }
  return currentMessage;
};

const logDebugInfo = (message: ConversationMessage) => {
  const debugConfig = window.__KWAIPILOT_AGENTS_DEBUG_CONFIG__ || {};
  if (!debugConfig.CHAT_RESPONSE_FLOW_DETAIL_ON) {
    return;
  }

  console.group('对话详情', message.chatId); // eslint-disable-line
  const workflowResponseDetail = message.workflowResponseDetail as
    | WorkflowDebugNodeDetail[]
    | undefined;
  if (!workflowResponseDetail) {
    return;
  }
  workflowResponseDetail.forEach((d) => {
    const sideDebugInfo: Record<string, unknown> = {};
    if (d.moduleType === 'datasetSearchNodeV2') {
      try {
        const texts = JSON.parse(d.textOutput) as string[];
        sideDebugInfo.texts = texts.map((t) => JSON.parse(t));
      } catch (err) {
        console.error('parse datasetSearchNodeV2 error:', err); // eslint-disable-line
      }
    }
    console.log(d.moduleName, { ...d, ...sideDebugInfo }); // eslint-disable-line
  });
  console.log('full workflow response:', workflowResponseDetail); // eslint-disable-line
  console.groupEnd(); // eslint-disable-line
};

/** 解析消息主体，附加到消息上 */
export const onSSEMessage = (
  event: {
    type: MessageType;
    reply: string;
    chatId?: number;
    replyChatId?: number;
    sseType?: string;
  },
  currentMessage: ConversationMessage,
  onViewCb?: () => void,
  onStartingResponse?: () => void,
  artifactsCallback?: (currentMessage: ConversationMessage) => void,
  options?: {
    translate?: (id: string, params?: Record<string, string | number>) => string;
  },
) => {
  if (event.type === 'ack') {
    console.debug('ack'); // eslint-disable-line
  }
  if (event.type === 'reply') {
    currentMessage.reply.normal += event.reply;
    currentMessage.content += event.reply;
    onStartingResponse?.();
    onViewCb?.();
  } else if (event.type === 'cot_reply') {
    currentMessage.reply.cot += event.reply;
    currentMessage = handleCotMessage(currentMessage, onStartingResponse);
    onViewCb?.();
  } else if (event.type === 'question') {
    currentMessage.content += event.reply;
    onViewCb?.();
  } else if (event.type === 'WEB_SEARCH') {
    /**
     * {
     *  "type": "WEB_SEARCH",
     *  "reply": [
     *      {
     *          link: 'xxx',
     *          title: 'xxx',
     *      }
     *  ]
     * }
     * NOTE: WEB_SEARCH 目前一定是最早的，所以有 WEB_SEARCH 就不用管其他的 source 了
     */
    currentMessage.reply.webSearch += event.reply;
    // TODO: @zhouhongxuan 类型缺失
    (JSON.parse(event.reply) as any).forEach((r: { link: string; title: string }) => {
      const sources = currentMessage.sources || [];
      sources.push({
        title: r.title,
        url: r.link,
      });
      currentMessage.sources = sources;
    });
    onViewCb?.();
  } else if (event.type === 'images') {
    try {
      const imgs = JSON.parse(event.reply) as Array<{
        title: string;
        imageUrl: string;
        link: string;
      }>;
      const images: RelatedImage[] = imgs.map((i) => ({
        url: i.imageUrl,
        sourceUrl: i.link,
      }));
      if (images.length > 0) {
        if (currentMessage.related) {
          currentMessage.related.images = images;
        } else {
          currentMessage.related = { images };
        }
      }
    } catch (e) {
      console.error('parse images error:', event, e);
    }
  }
  // 固定工具固定返回，可直接返回所有内容， 返回结构见https://docs.corp.kuaishou.com/k/home/<USER>/fcAB5ktPD7x-aMpnHW7T2cqma
  else if (event.type === 'stay_tool_call_response') {
    currentMessage.content += event.reply;
    currentMessage.messageType = event.type;
  } else if (event.type === 'error') {
    currentMessage.status = 'failed';
    currentMessage.content += `\n${event.reply}`;
  }
  // 触发风控会走到重置逻辑
  else if (event.type === 'content_reset') {
    currentMessage.status = 'success';
    currentMessage.content = event.reply;
    currentMessage.sources = undefined;
    currentMessage.related = undefined;
  } else if (event.type === 'workflow') {
    currentMessage.agentMode = 'WORKFLOW';
    onViewCb?.();
    if (event.sseType === 'answer' || event.sseType === 'fastAnswer') {
      onStartingResponse?.();
      if (event.reply === '[DONE]') {
        return;
      }
      try {
        const reply = JSON.parse(event.reply) as {
          id: string;
          object: string;
          created: number;
          model: string;
          choices: Array<{
            delta: {
              role: 'assistant' | 'user';
              content: string;
            };
            finish_reason: string | null;
            index: number;
          }>;
        };

        reply.choices.forEach((choice) => {
          if (choice.delta.role === 'assistant') {
            currentMessage.content += choice.delta.content;
          }
        });
      } catch (e) {
        console.error('parse workflow answer error:', e);
      }
    } else if (event.sseType === 'datasetSearchResult') {
      const reply = JSON.parse(event.reply) as {
        result: Array<{
          id: string;
          title: string;
          text: string;
          link: string;
        }>;
      };
      const sources = currentMessage.sources || [];
      reply.result.forEach((r) => {
        if (sources.length >= 5) {
          return;
        }
        let title = r.title;
        // 如果已经存在，则不再添加
        if (sources.find((s) => s.url === r.link)) {
          return;
        }

        const firstLine = r.text.split(/\r\n|\r|\n/)[0];
        if (!title && firstLine) {
          title = firstLine.replace(/^\s*#\s+/, '');
        }
        sources.push({
          title: title,
          url: r.link,
        });
        currentMessage.sources = sources;
      });
    } else if (event.sseType === 'flowResponses') {
      try {
        currentMessage.workflowResponseDetail = JSON.parse(
          event.reply,
        ) as WorkflowDebugNodeDetail[];
        logDebugInfo(currentMessage);
      } catch (e) {
        console.error('parse workflow response error:', e); // eslint-disable-line
      }
    } else if (event.sseType === 'toolCall') {
      currentMessage.reply.cot += event.reply + '\n';
      const process = currentMessage.process ?? {};
      process.calls = process.calls ?? [];
      const { tool } = JSON.parse(event.reply) as {
        tool: {
          id: string;
          toolName: string;
          toolAvatar: string;
          functionName: string;
          params: string;
        };
      };
      process.calls.push({
        type: BlockType.Thought,
        content: options?.translate
          ? options.translate('k_1292073', { name: tool.toolName })
          : `我需要调用 ${tool.toolName} 来回答`,
      });
      process.calls.push({
        id: tool.id,
        name: tool.toolName,
        cot: [
          {
            type: BlockType.Action,
            reply: tool.toolName,
          },
          {
            type: BlockType.ActionInput,
            reply: tool.params ?? '',
          },
        ],
      });
      currentMessage.process = process;
    } else if (event.sseType === 'toolResponse') {
      currentMessage.reply.cot += event.reply + '\n';
      if (currentMessage.process?.calls) {
        const { tool } = JSON.parse(event.reply) as {
          tool: {
            id: string;
            response: string;
          };
        };
        const call = currentMessage.process.calls.find((c) => c.id && c.id === tool.id);
        if (call && 'cot' in call) {
          let response = tool.response;
          if (call.name === '搜索引擎') {
            try {
              const result = JSON.parse(tool.response) as {
                answerBox: {
                  answer: string;
                  source: string;
                  sourceLink: string;
                  title: string;
                };
                organic: Array<{
                  link: string;
                  position: number;
                  snippet: string;
                  title: string;
                }>;
                relatedSearches: Array<{
                  query: string;
                }>;
                searchParameters: {
                  engine: string;
                  gl: string;
                  hl: string;
                  num: number;
                  q: string;
                  type: string;
                };
              };
              result.organic.forEach((r) => {
                const sources = currentMessage.sources || [];
                if (sources.length >= 5) {
                  return;
                }
                sources.push({
                  title: r.title,
                  url: r.link,
                });
                currentMessage.sources = sources;
              });
              response = JSON.stringify(result.organic);
            } catch {}
          }
          call.cot.push({
            type: BlockType.Observation,
            reply: response,
          });
        }
      }
    } else if (event.sseType === 'artifact') {
      const reply = JSON.parse(event.reply) as ArtifactChatItem;
      if (!currentMessage.artifacts) {
        /** 在这里打开 artifacts 弹窗 */
        artifactsCallback?.(currentMessage);
        currentMessage.artifacts = {
          process: [],
        };
      }
      if (reply.type === 'process') {
        const data = reply?.data;
        /** 代码生成 */
        if (data.category === 'code-gen') {
          formatCodeGenArtifactProcess(data, currentMessage);
        }
      } else if (reply.type === 'result') {
        currentMessage.artifacts.result = reply;
      } else if (reply.type === 'reset') {
        currentMessage.artifacts = undefined;
      }
    } else if (event.sseType === 'quoteSource') {
      const res = JSON.parse(event.reply) as { list: QuoteSource[] };
      currentMessage.quoteSources = res.list;
    } else if (event.sseType === 'datasetCodeSearchResult') {
      const res = JSON.parse(event.reply) as {
        result: CodeSearchResult[];
      };

      currentMessage.repoChatSources = res.result;
    }
  } else {
    console.warn('unknown event:', event);
  }
};

const formatCodeGenArtifactProcess = (
  data: ArtifactProcessChatItemCodeGen,
  currentMessage: ConversationMessage,
) => {
  const { content } = data;
  const { file, delta } = content;
  const { code } = delta;
  const currentProcess = currentMessage.artifacts?.process.find((i) => {
    const codeGenProcess = i.data as unknown as ArtifactProcessChatItemCodeGen;
    const _file = codeGenProcess.content.file;
    return _file === file;
  });
  if (!currentProcess) {
    currentMessage.artifacts = currentMessage.artifacts ?? {
      process: [],
    };
    currentMessage.artifacts.process.push({
      type: 'process',
      data,
    });
  } else {
    currentProcess.data.content.delta.code += code;
  }
};
