import { v4 as uuid } from 'uuid';

type ReportStage =
  | 'e2e-first-response'
  | 'e2e-done-response'
  | 'e2e-exception-response'
  | 'e2e-starting-response'
  | 'e2e-agent-done-response'
  | 'e2e-agent-exception-response'
  | 'e2e-agent-starting-response';

interface CostReportData {
  namespace: string;
  username: string;
  platform: string;
  modelType: string;
  version: string;
  hasReportFirst: boolean;
  hasReportStarting: boolean;
  hasReportDone: boolean;
  beginTimestamp: number;
  endTimestamp: number;
  duration: number;
  sessionId: string;
  requestId: string;
  stage: ReportStage;
  exception: string;
  extra1: boolean | string;
  extra2: boolean | string;
  extra3: number | string;
  extra4: string;
}

export class CostReporting {
  private commonData: Omit<
    CostReportData,
    | 'endTimestamp'
    | 'duration'
    | 'stage'
    | 'exception'
    | 'extra1'
    | 'extra4'
    | 'hasReportFirst'
    | 'hasReportStarting'
    | 'hasReportDone'
  >;
  private reportUrl: string = '/eapi/kwaipilot/log/time';
  private hasReportFirst: boolean = false;
  private hasReportStarting: boolean = false;
  private hasReportDone: boolean = false;

  constructor(
    namespace: string,
    platform: string,
    version: string,
    username: string,
    modelType: string,
    requestId?: string,
    options?: {
      url?: string;
    },
  ) {
    this.commonData = {
      namespace,
      platform,
      version,
      username,
      modelType,
      beginTimestamp: +new Date(),
      sessionId: uuid(),
      requestId: requestId || '',
      extra2: false,
      extra3: -1,
    };
    if (options?.url) {
      this.reportUrl = options.url;
    }
  }

  setRequestId(requestId: string) {
    this.commonData.requestId = requestId;
  }

  updateUsername(username: string) {
    this.commonData.username = username;
  }

  resetCommonData(data: {
    modelType: string;
    requestId: string;
    extra2?: boolean | string;
    extra3?: number | string;
  }) {
    const { modelType, requestId, extra2, extra3 } = data;
    this.commonData = {
      ...this.commonData,
      modelType,
      beginTimestamp: +new Date(),
      sessionId: uuid(),
      requestId,
      extra2: extra2 || false,
      extra3: extra3 || -1,
    };
    this.hasReportFirst = false;
    this.hasReportStarting = false;
    this.hasReportDone = false;
  }

  reportCost(data: {
    stage: ReportStage;
    exception?: string;
    extra1: boolean | string;
    extra2?: boolean | string;
    extra3?: number | string;
    extra4?: string;
  }) {
    const { stage, exception = '', extra1, extra2 = false, extra3 = -1, extra4 = '' } = data;
    if (stage === 'e2e-first-response' || stage === 'e2e-agent-starting-response') {
      if (this.hasReportFirst) return;
      this.hasReportFirst = true;
    }

    if (stage === 'e2e-starting-response' || stage === 'e2e-agent-starting-response') {
      if (this.hasReportStarting) return;
      this.hasReportStarting = true;
    }

    if (stage.includes('done') || stage.includes('exception')) {
      if (this.hasReportDone) return;
      this.hasReportDone = true;
    }

    const now = +new Date();
    const reportData: CostReportData = {
      ...this.commonData,
      hasReportFirst: this.hasReportFirst,
      hasReportStarting: this.hasReportStarting,
      hasReportDone: this.hasReportDone,
      stage,
      endTimestamp: now,
      duration: now - this.commonData.beginTimestamp,
      exception,
      extra1,
      extra2,
      extra3,
      extra4,
    };

    this.sendReport(reportData);
  }

  private sendReport(data: CostReportData) {
    fetch(this.reportUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }).catch((e) => {
      console.error('Report Cost Error: ', e);
    });
  }
}
