import { fetchEventSource } from '@fortaine/fetch-event-source';

import type { KapWebChatRequest } from '../services/data-contracts';
import { MOCK_SSE_DATA } from './const';

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

export const fetchStream = <T = KapWebChatRequest>(options: {
  url?: string;
  data: T;
  onMessage: (data: string) => void;
  onError: (e: any) => void;
  onClose: () => void;
  signal: AbortSignal;
  headers?: Record<string, string>;
}) => {
  const {
    data,
    onMessage,
    onError,
    onClose,
    signal,
    url = '/api/kap/conversation/chat',
    headers,
  } = options;

  fetchEventSource(url, {
    method: 'POST',
    body: JSON.stringify(data),
    headers: {
      'Content-type': 'application/json;charset=UTF-8',
      ...headers,
      // "trace-context": '{"laneId":"PRT"}'
    },
    onclose: () => onClose(),
    onmessage: (ev) => onMessage(ev.data),
    onerror: (e) => {
      onError(e);
      // 如果不抛出错误，就会自动重试
      // https://github.com/Azure/fetch-event-source/blob/a0529492576e094374602f24d5e64b3a271b4576/src/fetch.ts#L129-L136
      throw e;
    },
    signal,
    openWhenHidden: true,
  });
};

export const fetchStreamMock = (options: {
  data: {
    question: string;
    conversationId: number;
    fileIds?: number[];
  };
  onMessage: (data: string) => void;
  onError: (e: any) => void;
  onClose: () => void;
  signal: AbortSignal;
}) => {
  const { data, onMessage, onError, onClose, signal } = options;
  let stopped = false;
  signal.onabort = () => {
    stopped = true;
    onClose();
  };

  (async () => {
    for (const e of MOCK_SSE_DATA) {
      if (stopped) {
        return;
      }
      onMessage(e);
      await sleep(100);
    }
    onClose();
  })();
};
