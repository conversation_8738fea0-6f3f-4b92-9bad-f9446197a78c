import { ConversationMessage } from '../types';
import { appendCOTSourceToMessage, onSSEMessage } from './parse';

interface LlmFileUploadDO {
  biz?: string;
  bucketName?: string;
  filename?: string;
  /** @format int64 */
  id?: number;
  path?: string;
  /** @format int64 */
  size?: number;
  type?: string;
  url?: string;
  username?: string;
}

interface MessageModel {
  /** 内容 */
  content?: string;
  /**
   * 会话ID
   * @format int64
   */
  conversionId?: number;
  /** @format int64 */
  createTime?: number;
  files?: LlmFileUploadDO[];
  id?: number;
  /**
   * 问题对应的消息ID
   * @format int64
   */
  replyChatId?: number;
  /** 角色 */
  role?: string;
  /** 状态 */
  status?: number;
}

export const messageModel2ConversationMessage = <T extends ConversationMessage>(
  rawMessage: MessageModel,
  newMessage: () => T,
  options?: {
    translate?: (text: string, params?: Record<string, string | number>) => string;
  },
): T | undefined => {
  const message = newMessage();
  if (rawMessage.id) {
    message.chatId = String(rawMessage.id);
  }
  if ('status' in rawMessage) {
    message.status =
      rawMessage.status === 0
        ? 'stop'
        : rawMessage.status === 1
          ? 'success'
          : rawMessage.status === 2
            ? 'failed'
            : 'stop';
  }
  if (rawMessage.replyChatId) {
    message.replyChatId = String(rawMessage.replyChatId);
  }
  message.role = rawMessage.role === 'user' ? 'user' : 'assistant';
  message.createTime = rawMessage.createTime || -1;
  if (rawMessage.files) {
    message.refFiles = rawMessage.files
      .filter((f) => f)
      .map((f) => ({
        uuid: String(f.id),
        fileName: f.filename || '',
        fileSize: f.size || 0,
        fileType: f.type || '',
        fileUrl: f.url,
        fileId: String(f.id),
      }));
  }
  try {
    const replies = JSON.parse(rawMessage.content || '') as Array<{
      type: 'reply' | 'cot_reply' | 'WEB_SEARCH' | 'warning' | 'content_reset' | 'images';
      reply: string;
    }>;
    replies.forEach((r) => {
      onSSEMessage(r, message, undefined, undefined, undefined, options);
      appendCOTSourceToMessage(message);
    });
    return message;
  } catch (e) {
    console.warn('parse reply error:', e);
  }
};
