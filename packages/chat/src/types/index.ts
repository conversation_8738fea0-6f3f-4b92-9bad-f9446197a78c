import type { FileData } from '../components/FileCard';
import type { Message } from '../components/MessageCard/types';

/** 消息反馈 */
export enum FEEDBACK_TYPE_ENUM {
  'dislike' = -1,
  'cancel' = 0,
  'like' = 1,
}

export interface UserData {
  avatar?: string;
  username?: string;
  displayName?: string;
}

/** 消息体类型 */
export interface ConversationMessage extends Message {
  type:
    | 'message'
    | 'error'
    | 'context-overflow'
    | 'input-overflow'
    | 'message-divider'
    | 'no-network'
    | 'no-answer'
    | 'api-error';
  reply: {
    cot: string;
    normal: string;
    webSearch: string;
  };
  createTime: number;
  refFiles?: FileData[];

  rawData: string[];
  agentMode: 'WORKFLOW' | 'COMMON';
}

type FixedToolType = {
  id: number;
  name: string;
};

export interface AgentInfoType {
  icon?: string;
  name?: string;
  greetingWords?: string;
  recommends?: string[];
  // 固定工具位
  tools?: FixedToolType[];
}
