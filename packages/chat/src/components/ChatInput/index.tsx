import React from 'react';
import { NavigateFunction } from 'react-router-dom';

import { ajaxPipe } from '@ks-kwaipilot/utils';
import { Button, Input, Tooltip, TooltipProps, message } from 'antd';
import classNames from 'classnames';
import { v4 as uuidv4 } from 'uuid';

import AddIcon from '../../assets/add.svg?react';
import AiIcon from '../../assets/aiBadge.svg?react';
import ClearIcon from '../../assets/clear-hisotry.svg?react';
import NewChatIcon from '../../assets/new-chat.svg?react';
import { CommonIcon } from '../../common/get-icon';
import { MessageLlmFileUploadDO } from '../../services/data-contracts';
import { ContentType, HttpClient } from '../../services/http-client';
import useChatInputStore from '../../store/chatInput';
import type { AgentInfoType, UserData } from '../../types';
import AutoTooltip from '../AutoTooltip';
import FileCard, { FileData } from '../FileCard';
import css from './index.module.less';

const { TextArea } = Input;

const httpClient = new HttpClient();

export type UpdateOption = {
  input?: string;
  files?: FileData[];
};

interface Props {
  disable?: boolean;
  /** 禁用时提示 */
  disableTip?: string;
  disableUpload?: boolean;
  user: UserData;
  files: FileData[];
  input: string;
  isDisableNewChat?: boolean;
  needLightEffect?: boolean;
  highlight?: boolean;
  disclaimer?: boolean;
  tools: AgentInfoType['tools'];
  newChat?: () => void;
  update: (options: UpdateOption) => void;
  beforeSend?: () => void;
  afterSend?: () => void;
  navigate: NavigateFunction;
  status: 'running' | 'idle';
  sendMessage: (question: string, options: { files: FileData[] }) => Promise<boolean>;
  isSDKMode?: boolean;
  onClear?: () => void;
  onClickTool?: (id: number) => void;
}

interface State {
  isComposing: boolean; // 用于标识是否处于中文输入状态,
  /** 是否多行显示 */
  isMultiline: boolean;
}

export class ChatInput extends React.Component<Props, State> {
  private inputRef: React.RefObject<HTMLTextAreaElement> = React.createRef<HTMLTextAreaElement>();
  private isSending: boolean = false;
  private fileInputRef: React.RefObject<HTMLInputElement> = React.createRef<HTMLInputElement>();
  constructor(props: Props) {
    super(props);
    this.state = {
      isComposing: false,
      isMultiline: false,
    };
    this.handleKeyup = this.handleKeyup.bind(this);
  }

  componentDidMount(): void {
    document.addEventListener('keyup', this.handleKeyup);
    window.addEventListener('sdkSendMessage', this.handleSDKSendMessage);
    window.addEventListener('sdkSetQuestion', this.handleSDKSetQuestion);
  }
  componentWillUnmount(): void {
    document.removeEventListener('keyup', this.handleKeyup);
    window.removeEventListener('sdkSendMessage', this.handleSDKSendMessage);
    window.removeEventListener('sdkSetQuestion', this.handleSDKSetQuestion);
  }
  handleKeyup(e: KeyboardEvent) {
    // 只有没有聚焦在其他可输入的元素上时才可聚焦
    if (e.key === '/' && document.activeElement?.tagName === 'BODY') {
      this.focusInput();
    }
  }

  /**
   * 输入改变监听器
   */
  inputChange = (e: { target: EventTarget & HTMLTextAreaElement }) => {
    this.props.update({
      input: e.target.value,
    });
  };

  onFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadFiles = event.target.files;
    this.handleFileUpload({
      uploadFiles,
      onError: () => {
        // 清空文件输入框中的内容，避免上传超出大小的文件
        event.target.value = '';
      },
    });
  };

  handleFileUpload = async (param: { uploadFiles: FileList | null; onError?: () => void }) => {
    const { uploadFiles, onError } = param;
    const { user } = this.props;
    try {
      if (!uploadFiles || uploadFiles.length == 0) {
        return;
      }
      const curFileNames = new Set(this.props.files.map((obj) => obj.fileName));
      // 限制文件大小为 3MB（以字节为单位）
      const maxSizeInBytes = 3 * 1024 * 1024; // 3MB
      let totalFileSize = 0;

      // 已上传文件计算
      this.props.files.forEach((item) => {
        totalFileSize += item.fileSize;
      });

      // 本次上传文件计算
      for (let i = 0; i < uploadFiles.length; i++) {
        const fileSize = uploadFiles[i].size ?? 0;
        totalFileSize += fileSize;
      }

      if (totalFileSize > maxSizeInBytes) {
        message.error('文件总大小超过限制，上传文件总大小应小于3M');
        onError?.();
        return; // 如果文件大小超过限制，结束函数，不执行上传操作
      }

      for (let i = 0; i < uploadFiles.length; i++) {
        const file = uploadFiles[i];
        // 通过文件名校验
        if (curFileNames.has(file.name)) {
          message.error('文件已存在，请上传其他文件');
          return;
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('username', user.username || '');
        const uuid = uuidv4();

        const filedata: FileData = {
          uuid,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          thumbnailUrl: URL.createObjectURL(file),
          loading: true,
        };
        this.props.update({
          files: [...this.props.files, filedata],
        });
        await ajaxPipe(
          httpClient.request<MessageLlmFileUploadDO, void>({
            path: `/api/kwaipilot/file/upload`,
            method: 'POST',
            body: { file },
            type: ContentType.FormData,
          }),
        )
          .then((res) => {
            const idx = this.props.files.findIndex((i) => i.uuid === uuid);
            const item = this.props.files[idx];
            if (!item) {
              console.warn('file not found', uuid, this.props.files);
              return;
            }
            const fileItem: FileData = {
              ...item,
              loading: false,
              fileId: res?.id ? String(res.id) : undefined,
              fileUrl: res?.url,
            };
            this.props.files[idx] = fileItem;
            this.props.update({
              files: [...this.props.files],
            });
            message.success({
              content: '上传成功',
            });
          })
          .catch((e) => {
            message.error('文件上传失败，请重试');
            console.error(e);
            this.deleteFile(uuid);
          });
      }
    } finally {
      if (this.fileInputRef.current) {
        this.fileInputRef.current.value = '';
      }
    }
  };

  deleteFile = (uuid: string) => {
    const files = this.props.files.filter((obj) => obj.uuid !== uuid);
    this.props.update({
      files,
    });
  };
  onClickFileUpload = () => {
    this.fileInputRef.current?.click();
  };
  handlePasteEvent = (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const items = event.clipboardData?.items;
    if (items) {
      const files: File[] = [];
      for (const item of items) {
        if (item.type.startsWith('image/')) {
          const file = item.getAsFile();
          if (file) {
            const newFile = new File([file], `${uuidv4()}.${file.name?.split('.')?.pop()}`, {
              type: file.type,
              lastModified: file.lastModified,
            });
            files.push(newFile);
          }
        }
      }
      if (files.length > 0) {
        if (this.props.disableUpload) {
          message.warning('当前不支持上传附件', 3);
          return;
        }
        const dataTransfer = new DataTransfer();
        files.forEach((file) => dataTransfer.items.add(file));
        const fileList = dataTransfer.files;
        this.handleFileUpload({ uploadFiles: fileList });
      }
    }
  };

  // 输入法开始输入监听
  onCompositionStart = () => {
    this.setState({ isComposing: true });
  };

  // 输入法结束输入监听
  onCompositionEnd = () => {
    this.setState({ isComposing: false });
  };

  /**
   * 发送｜换行监听器
   * @param e
   * @returns
   */
  sendListener = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const { isComposing } = this.state;
    // Safari 下中文回车兼容
    if (e.keyCode == 229) return false;
    if (isComposing) {
      // 处理中文输入状态，不执行任何操作
      return;
    }
    if (e.shiftKey && e.key === 'Enter') {
      // 换行
      this.setState(
        {
          // input: input + '\n'
        },
        () => {
          const inputWindow = document.querySelector('#start-panel #textInputComponent');
          if (inputWindow) {
            // 手动触发onChange事件
            this.inputChange({
              target: inputWindow as HTMLTextAreaElement,
            });
            inputWindow.scrollTop = inputWindow.scrollHeight;
          }
        },
      );
      return;
    }
    if (e.key != 'Enter') {
      return;
    }

    e.preventDefault();

    if (this.props.input.trim() === '') {
      message.error('请输入对话');
      return;
    }

    this.onClickSendMsg();
  };

  clearInput = () => {
    this.props.update({
      input: '',
      files: [],
    });
    // this.setState({ isMultiline: false });
  };

  onClickSendMsg = async () => {
    if (this.props.status === 'running') {
      message.info('对话进行中，请稍后切换');
      return;
    }
    try {
      // 避免同时发送多次
      if (this.isSending) {
        return;
      }

      this.isSending = true;
      await this.props.beforeSend?.();

      await this.props.sendMessage(this.props.input ?? '', { files: this.props.files });
      this.clearInput();

      this.props.afterSend?.();
    } finally {
      this.isSending = false;
    }
  };

  focusInput = () => {
    const inputElement = this.inputRef.current;
    if (inputElement) {
      inputElement.focus();
    }
  };

  // sdk 触发发送消息
  handleSDKSendMessage = (e: Event) => {
    const { type, content } = (e as CustomEvent).detail;
    if (type === 'text') {
      useChatInputStore.getState().setInput(content);

      this.onClickSendMsg();
    }
  };

  // sdk 触发将内容作为待发送文本放入输入框
  handleSDKSetQuestion = (e: Event) => {
    const { type, content } = (e as CustomEvent).detail;
    if (type === 'text') {
      this.props.update({
        input: content,
      });
    }
  };

  render() {
    const maxWordCount = 30000;
    const { disableTip, disable, newChat, isDisableNewChat, tools, onClickTool } = this.props;
    const tooltipExtStyle: TooltipProps = {
      overlayInnerStyle: {
        color: 'rgba(255,255,255, 0.88)',
      },
      color: '#252626',
    };

    const uploadFileBtn = (
      <Tooltip title="支持上传文件（总文件大小不超过3MB）接受pdf、doc、txt等">
        <div
          onClick={this.onClickFileUpload}
          style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
          data-ui-test-info={`输入框-上传`}
        >
          <div className={`${css.fileSend} ${this.props.disableUpload ? css.disabled : ''}`}>
            {this.props.disableUpload ? (
              <CommonIcon name="uploadDisableIcon" />
            ) : (
              <CommonIcon name="uploadIcon" />
            )}
          </div>
          <input
            accept="text/*,application/pdf,.kt,application/msword,.doc,.docx,image/*"
            multiple
            type="file"
            ref={this.fileInputRef}
            onChange={this.onFileUpload}
            style={{ display: 'none' }}
          />
        </div>
      </Tooltip>
    );
    const sendMessageBtn =
      this.props.input.trim() == '' ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Tooltip title="请输入对话" {...tooltipExtStyle}>
            <div className={`${css.iconDisableDiv}`}>
              <CommonIcon name="senderDisableIcon" />
            </div>
          </Tooltip>
        </div>
      ) : (
        <Tooltip title="发送" {...tooltipExtStyle}>
          <div
            onClick={this.onClickSendMsg}
            style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
            data-ui-test-info={`输入框-发送`}
          >
            <div className={`${css.iconHoverDiv} ${css.sendIcon}`}>
              <CommonIcon name="senderIcon" />
            </div>
          </div>
        </Tooltip>
      );

    return (
      <div className={css['container']} style={this.props.isSDKMode ? { width: '100%' } : {}}  data-ui-test-info={`输入框`}>
        <div className={css.left}>
          {!this.props.isSDKMode && newChat && (
            <AddIcon
              onClick={isDisableNewChat ? undefined : newChat}
              style={{
                cursor: isDisableNewChat ? 'not-allowed' : 'pointer',
                color: isDisableNewChat ? '#D5D6D9' : '#898A8C',
              }}
              className={css.chatInputNewChat}
            />
          )}
        </div>
        <div className={css.right}>
          {this.props.isSDKMode && (
            <div className={css.oncallActions}>
              <div>
                {!!tools?.length && (
                  <div className={css['oncall-tool-container']}>
                    {tools.map((tool) => (
                      <div
                        key={tool.id}
                        className={css['oncall-action-tool']}
                        onClick={() => onClickTool?.(tool.id)}
                      >
                        <AutoTooltip
                          title={tool.name}
                          className={css['oncall-tool-content']}
                          lineClamp={1}
                        >
                          {tool.name}
                        </AutoTooltip>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <div className={css.oncallActionsRight}>
                <Tooltip
                  overlayInnerStyle={{
                    color: 'rgba(255,255,255, 0.88)',
                  }}
                  title={isDisableNewChat ? '消息为空，不支持新建' : '新建对话'}
                  color="#252626"
                >
                  <NewChatIcon
                    className={isDisableNewChat ? undefined : css.opHover}
                    onClick={isDisableNewChat ? undefined : newChat}
                    style={{
                      cursor: isDisableNewChat ? 'not-allowed' : 'pointer',
                      color: isDisableNewChat ? '#D5D6D9' : '#898A8C',
                    }}
                  />
                </Tooltip>
                <Tooltip
                  overlayInnerStyle={{
                    color: 'rgba(255,255,255, 0.88)',
                  }}
                  title="清空对话"
                  color="#252626"
                >
                  <ClearIcon
                    className={css.opHover}
                    onClick={this.props.onClear}
                    style={{ cursor: 'pointer' }}
                  />
                </Tooltip>
              </div>
            </div>
          )}
          {!!tools?.length && !this.props.isSDKMode && (
            <div className={css['tool-container']}>
              {tools.map((tool) => (
                <Button key={tool.id} onClick={() => onClickTool?.(tool.id)}>
                  {tool.name}
                </Button>
              ))}
            </div>
          )}
          <div
            style={
              this.props.isSDKMode
                ? { minHeight: '28px', padding: '7px 8px', borderRadius: '7px' }
                : {}
            }
            className={classNames(
              css.chatInputAreaDiv,
              { [css.multiLine]: this.state.isMultiline },
              {
                [css.highlight]: this.props.highlight,
              },
            )}
          >
            {this.props.files.length > 0 && (
              <div className={css.fileInputArea} style={{ width: '100%' }}>
                {this.props.files.map((item, index) => {
                  return (
                    <div key={index}>
                      <FileCard fileData={item} onDelete={() => this.deleteFile(item.uuid)} />
                    </div>
                  );
                })}
              </div>
            )}
            <div className={css['chatContentContainer']}>
              <div className={classNames(css.chatContentDiv)}>
                <Tooltip title={disable ? disableTip : ''}>
                  <TextArea
                    disabled={disable}
                    ref={this.inputRef as any}
                    maxLength={maxWordCount}
                    autoComplete={'on'}
                    id="textInputComponent"
                    onKeyDown={this.sendListener}
                    autoSize={true}
                    value={this.props.input}
                    onChange={this.inputChange}
                    className={classNames(css.chatTextArea, disable ? css.disableInput : '')}
                    placeholder={this.props.isSDKMode ? '有问题尽管问我...' : '说说你想问什么'}
                    onCompositionStart={this.onCompositionStart}
                    onCompositionEnd={this.onCompositionEnd}
                    onPaste={this.handlePasteEvent}
                    variant="borderless"
                    autoFocus
                  />
                </Tooltip>
              </div>

              <div className={css.chatInputToolArea}>
                {uploadFileBtn}
                {sendMessageBtn}
              </div>
            </div>
          </div>

          {this.props.disclaimer && (
            <div className={css.tipTitleRow}>
              {this.props.isSDKMode ? (
                <div className={css.tipTitle} style={{ lineHeight: '18px' }}>
                  powered by Kwaipilot
                </div>
              ) : (
                <>
                  <span className={css.tipTitle}>内容由</span>
                  <AiIcon />

                  <span className={css.tipTitle}>大模型生成，采纳请仔细甄别</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }
}
