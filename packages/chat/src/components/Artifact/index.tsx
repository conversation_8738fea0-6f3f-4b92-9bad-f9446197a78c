// 当前设计只允许一个产物
import React, { Suspense, lazy, useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

import {
  ArtifactProcessChatItem,
  ArtifactResultChatItemFilePreview,
  ArtifactResultChatItemWebPreview,
  WEBPAGE_PREVIEW_BASE,
  getLanguageIdByFileName,
} from '@ks-kwaipilot/utils';
import { message as antMessage } from 'antd';
import copy from 'copy-to-clipboard';

import ArtifactIcon from '@/assets/artifact.svg?react';
import CloseIcon from '@/assets/close.svg?react';

import { ConversationMessage } from '../../types';
import BtnTab from './components/BtnTab';
import CodeGen from './components/CodeGen';
import { Empty } from './components/Empty';
import { Loading } from './components/Loading';
import { ArtifactsTab, ProduceTab } from './components/Tab';
import Web from './components/Web';
import ImagePreview from './components/imgPreview';

/** 单产物 */
const monoproduct = ['web', 'webide', 'code-gen'];
/** 多产物 */
const multiproduct = ['file'];

const ExcelPreview = lazy(() => import('./components/ExcelPreview'));
const WordPreview = lazy(() => import('./components/WordPreview'));
const CsvPreview = lazy(() => import('./components/CsvPreview'));

interface IProps {
  closeArtifactDialog: () => void;
  message?: ConversationMessage;
  shown?: boolean;
}

export const Artifact: React.FC<IProps> = ({ message, closeArtifactDialog, shown }) => {
  const [processActive, setProcessActive] = useState(0);
  const [resultActive, setResultActive] = useState(0);
  const [artifactsTab, setArtifactsTab] = useState<'process' | 'result'>('process');

  const artifactsTabs: {
    key: 'process' | 'result';
    title: string;
  }[] = useMemo(() => {
    return [
      {
        key: 'process',
        title: 'Code',
      },
      {
        key: 'result',
        title: '预览',
      },
    ];
  }, []);

  const changeArtifactsTab = (key: 'process' | 'result') => {
    setArtifactsTab(key);
  };

  const title = useMemo(() => {
    return '产物生成预览';
  }, []);

  const produceTabs = useMemo(() => {
    /** process 目前只有 code-gen */
    if (artifactsTab === 'process') {
      return message?.artifacts?.process.map((item: ArtifactProcessChatItem, idx: number) => {
        return {
          key: idx,
          title: item.data.content.file,
        };
      });
    }
    if (artifactsTab === 'result') {
      if (multiproduct.includes(message?.artifacts?.result?.data.category || '')) {
        if (message?.artifacts?.result?.data.category === 'file') {
          return message?.artifacts?.result?.data.content.files.map((item, idx) => {
            return {
              key: idx,
              title: item.name,
            };
          });
        }
        /** 其他类型 */
      }
    }
  }, [artifactsTab, message?.artifacts?.process, message?.artifacts?.result]);

  /** 设置生成产物 tab */
  const produceTabChange = (key: number) => {
    if (artifactsTab === 'process') {
      setProcessActive(key);
    }
    if (artifactsTab === 'result') {
      setResultActive(key);
    }
  };

  const currentProduce = useMemo(() => {
    if (artifactsTab === 'process') {
      return message?.artifacts?.process[processActive];
    }

    if (artifactsTab === 'result') {
      if (message?.artifacts?.result?.data.category === 'file') {
        return message?.artifacts?.result?.data.content.files[resultActive];
      }

      /** 其他类型 */

      if (monoproduct.includes(message?.artifacts?.result?.data.category || '')) {
        return message?.artifacts?.result;
      }
    }
  }, [
    artifactsTab,
    message?.artifacts?.process,
    message?.artifacts?.result,
    processActive,
    resultActive,
  ]);

  const renderArtifact = useMemo(() => {
    if (artifactsTab === 'process') {
      if (currentProduce?.type === 'process') {
        if (currentProduce.data.category === 'code-gen') {
          return (
            <CodeGen
              code={currentProduce.data.content.delta.code}
              languageId={getLanguageIdByFileName(currentProduce.data.content.file)}
            />
          );
        }
      }
    }

    if (artifactsTab === 'result') {
      if (currentProduce?.type === 'result') {
        if (currentProduce.data.category === 'web') {
          return <Web url={currentProduce.data.content.url}></Web>;
        }
        if (currentProduce.data.category === 'code-gen') {
          return (
            <CodeGen
              code={currentProduce.data.content.delta.code}
              languageId={getLanguageIdByFileName(currentProduce.data.content.file)}
            />
          );
        }
      }
      if (message?.artifacts?.result?.data.category === 'file') {
        if (currentProduce?.type === 'image') {
          return <ImagePreview src={currentProduce.url}></ImagePreview>;
        }

        if (currentProduce?.type === 'txt') {
          return (
            <CodeGen
              code={currentProduce.content}
              languageId={getLanguageIdByFileName(currentProduce.name)}
            />
          );
        }

        if (currentProduce?.type === 'pdf') {
          return (
            <embed src={currentProduce.url} type="application/pdf" width="100%" height="100%" />
          );
        }

        if (currentProduce?.type === 'csv') {
          return (
            <Suspense fallback={<Loading></Loading>}>
              <CsvPreview csvUrl={currentProduce.url} />
            </Suspense>
          );
        }

        if (currentProduce?.type === 'file') {
          return null;
        }
        if (currentProduce?.type === 'excel') {
          return (
            <Suspense fallback={<Loading></Loading>}>
              <ExcelPreview excelUrl={currentProduce.url} />
            </Suspense>
          );
        }
        if (currentProduce?.type === 'doc') {
          return (
            <Suspense fallback={<Loading></Loading>}>
              <WordPreview url={currentProduce.url} />
            </Suspense>
          );
        }
      }
    }

    return null;
  }, [artifactsTab, currentProduce, message?.artifacts?.result]);

  /** 初始化 key*/
  useEffect(() => {
    setProcessActive(0);
    setResultActive(0);
    setArtifactsTab('process');
  }, [message?.chatId]);

  const processLen = useMemo(() => {
    return message?.artifacts?.process.length || 0;
  }, [message?.artifacts?.process]);

  const closeDialog = () => {
    closeArtifactDialog();
    setTimeout(() => {
      setArtifactsTab('process');
      setProcessActive(0);
      setResultActive(0);
    }, 200);
  };

  useEffect(() => {
    if (shown) {
      if (message && message.artifacts?.result) {
        setArtifactsTab('result');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shown]);

  /** 自动切花案process */
  useEffect(() => {
    if (processActive === processLen - 2) {
      setProcessActive(processActive + 1);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [processLen]);

  /** 切换到 result菜单 */
  useEffect(() => {
    if (message?.artifacts?.result && processActive === processLen - 1) {
      changeArtifactsTab('result');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [message?.artifacts?.result]);

  const { id, auid } = useParams();

  const shareUrl = useMemo(() => {
    const isWeb = message?.artifacts?.result?.data.category === 'web';
    if (isWeb) {
      const url = (
        message?.artifacts?.result?.data?.content as ArtifactResultChatItemWebPreview['content']
      )?.url;
      if (url) {
        let type: keyof typeof WEBPAGE_PREVIEW_BASE | null = null;
        for (const [key, value] of Object.entries(WEBPAGE_PREVIEW_BASE)) {
          if (url && url.startsWith(value)) {
            type = key as keyof typeof WEBPAGE_PREVIEW_BASE;
          }
        }

        if (!type) {
          return null;
        }

        const base = WEBPAGE_PREVIEW_BASE[type];
        const path = url.slice(base.length);
        const link = new URL('/agent-webgen-share', window.location.origin);
        link.searchParams.append('web', encodeURIComponent(path));
        link.searchParams.append('id', id || auid || '');
        link.searchParams.append('type', type);
        return link.toString();
      }
    }
    return null;
  }, [message?.artifacts?.result, id, auid]);

  const clickShare = useCallback(() => {
    if (shareUrl) {
      copy(shareUrl);

      antMessage.success('复制成功');
    }
  }, [shareUrl]);

  const downloadUrl = useMemo(() => {
    if (message?.artifacts?.result?.data.category === 'file') {
      if (currentProduce) {
        return (currentProduce as ArtifactResultChatItemFilePreview['content']['files'][number])
          .url;
      }
    }
    return undefined;
  }, [message?.artifacts?.result, currentProduce]);


  useEffect(() => {
    if (!message) {
      closeArtifactDialog();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [message]);

  if (!message) {
    return null;
  }

  return (
    <div className="flex h-full flex-col">
      <div className="flex h-[52px] items-center justify-between px-[16px] py-[12px]">
        <div className="flex items-center justify-start">
          <ArtifactIcon />
          <div className="color-[#252626] ml-[8px] text-[14px] font-medium leading-[22px]">
            {title}
          </div>
        </div>
        <div className="flex items-center justify-end">
          <ArtifactsTab
            tabs={artifactsTabs}
            activeKey={artifactsTab}
            onChange={changeArtifactsTab}
          />
          <div className="ml-[8px] cursor-pointer" onClick={closeDialog}>
            <CloseIcon />
          </div>
        </div>
      </div>
      {produceTabs && produceTabs.length ? (
        <ProduceTab
          tabs={produceTabs}
          onChange={produceTabChange}
          activeKey={artifactsTab === 'process' ? processActive : resultActive}
        ></ProduceTab>
      ) : null}
      <div className="flex-1 overflow-auto">
        {renderArtifact ? renderArtifact : <Empty tip="暂不支持预览或数据为空"></Empty>}
      </div>
      <BtnTab clickShare={shareUrl ? clickShare : undefined} url={downloadUrl}></BtnTab>
    </div>
  );
};
