import type { Artifacts } from '@ks-kwaipilot/utils';
import { BlockType } from '@ks-kwaipilot/utils/src/types/message';

import type { WorkflowDebugNodeDetail } from '../../utils/parse';

export enum ChatMode {
  FullWebSearch = 'fullWebSearch',
  KnowledgeRepo = 'knowledgeRepo',
  CodeRepo = 'codeRepo',
}

export interface QuoteRepoSource {
  type: 'repo';
  code: string;
  startLineNo: number;
  endLineNo: number;
  startColNo: number;
  endColNo: number;
  language: string;
  path: string;
  repoName: string;
  fileName: string;
  commitId: string;
}

export interface QuoteWebSource {
  type: 'web';
  title: string;
  link: string;
  date: string;
  favicon: string;
  content: string;
  prevContent: string;
  nextContent: string;
}

export interface QuoteFileSource {
  type: 'file';
  filename: string;
  url: string;
  pageNum: number;
  content: string;
}

export interface QuoteDocSource {
  type: 'doc';
  content: string;
  link: string;
  title: string;
  knowledgeRepoId: string;
  knowledgeRepoName: string;
}

/** 引用来源格式 */
export type QuoteSource = QuoteRepoSource | QuoteWebSource | QuoteFileSource | QuoteDocSource;

export type CodeSearchResult = {
  id: string;
  code: string;
  startLineNo: number;
  endLineNo: number;
  startColNo: number;
  endColNo: number;
  language: string;
  path: string;
  repoName: string;
  fileName: string;
  commitId: string;
};

export interface WebSearchResult {
  url: string;
  title: string;
}

/** 引用来源格式 */
export type SourceType = {
  title: string;
  url: string;
};

export type RelatedImage = {
  /** 图片链接 */
  url: string;
  /** 图片网页来源 */
  sourceUrl: string;
};

export type MessageSpecialType = 'stay_tool_call_response';
// /** 建议问题 */
// export type SuggestQuestion = {
//   /** 问题 */
//   question: string;
//   /** 相关度 */
/** 知识库类型 */
export type KnowledgeType = {
  id: number;
  name: string;
};

/** 代码仓库类型 */
export type CodeRepoType = {
  id: number;
  name: string;
};
export interface Message {
  chatId: string;
  replyChatId: string;
  status: 'pending' | 'success' | 'failed' | 'stop';
  /** 回答内容 */
  content: string;
  role: 'user' | 'assistant' | 'system';
  /** 引用文件 */
  refFiles?: FileData[];
  /** 消息处理过程，包含多个工具调用阶段 */
  process?: {
    spend?: ProcessTime;
    calls?: (ToolCallProcess | ThoughtCallProcess)[];
  };
  /** 信息来源 */
  sources?: SourceType[];
  /** 消息相关其他信息 */
  related?: {
    images: RelatedImage[];
  };
  /** 猜你想问 */
  suggestQuestions?: string[];
  /** 工作流执行的详细信息，用于展示和调试 */
  workflowResponseDetail?: WorkflowDebugNodeDetail[];
  /** 特殊类型的消息体，有些需要专门处理 */
  messageType?: MessageSpecialType;
  /** 仓库代码搜索来源 */
  repoChatSources?: CodeSearchResult[];
  /** artifacts产物和过程 */
  artifacts?: Artifacts;
  /** 信息来源 */
  quoteSources?: QuoteSource[];
}

/** 简化版的消息体，目前用于用户问题 */
export interface SimpleMessage extends Pick<Message, 'content' | 'refFiles' | 'role' | 'status'> {}

/** 处理时间耗时 */
interface ProcessTime {
  /** 处理总耗时 */
  totalTime: number;
  /** 模型耗时耗时 */
  modelTime: number;
  /** 工具调用耗时 */
  toolTime: number;
}

/** 思考链类型 */
export interface CotType {
  type: BlockType;
  reply: string;
}

export interface ToolCallProcess {
  id?: string;
  // icon: string;
  /** 调用的工具名称 */
  name: string;
  /** 思考链 */
  cot: CotType[];
  /** 每个工具耗时 */
  time?: ProcessTime;
}

/** 模型思考过程 */
export interface ThoughtCallProcess {
  id?: string;
  // 这里目前只有 thought 这一种
  type: BlockType.Thought;
  content: string;
}

export interface FileData {
  uuid: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl?: string;
  fileId?: string;
  loadding?: boolean;
}
