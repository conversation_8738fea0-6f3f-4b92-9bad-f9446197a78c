import React, { memo } from 'react';

import { AgentsMarkDown, SDKMarkDown } from '@ks-kwaipilot/markdown-render';
import { message } from 'antd';
import copy from 'copy-to-clipboard';

import { Message, MessageSpecialType } from './types';

export interface IContentProps {
  status: Message['status'];
  content: string;
  className?: string;
  isSDKMode?: boolean;
  messageType?: MessageSpecialType;
  openImageViewer?: (src: string) => void;
  message?: Message;
  handleArtifactClick?: (chatId: string) => void;
  openImageViewerList?: (urls: string[], currentIndex: number, filename?: string) => void;
}

type Props = IContentProps;

const Content: React.FC<Props> = ({
  content,
  className,
  isSDKMode,
  messageType,
  status,
  openImageViewer,
  message: llmMessage,
  handleArtifactClick,
  openImageViewerList,
}) => {
  const handleCopy = async (content: string) => {
    await copy(content);
    message.success('复制成功');
  };

  const artifactClick = () => {
    if (handleArtifactClick) {
      if (llmMessage) {
        if (llmMessage.artifacts) {
          if (llmMessage.artifacts.process.length || llmMessage.artifacts.result) {
            handleArtifactClick?.(llmMessage.chatId);
          }
        } else {
          message.error('暂无产物');
        }
      } else {
        message.error('消息不存在');
      }
    } else {
      message.error('暂不支持Artifacts');
    }
  };
  const MarkdownComponent = isSDKMode ? SDKMarkDown : AgentsMarkDown;
  return (
    <MarkdownComponent
      content={content}
      codeCopy={handleCopy}
      imgPreview={openImageViewer}
      messageType={messageType}
      status={status}
      containerClassName={className}
      handleArtifactClick={artifactClick}
      openImageViewerList={openImageViewerList}
    ></MarkdownComponent>
  );
};

export default memo(Content);
