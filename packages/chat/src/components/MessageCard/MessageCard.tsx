import React, { memo, useCallback, useState } from 'react';

import { Avatar, message as toast } from 'antd';
import copy from 'copy-to-clipboard';

import { CommonIcon } from '../../common/get-icon';
import { AgentInfoType, UserData } from '../../types';
import Actions from './Actions';
import Content from './Content';
import style from './MessageCard.module.less';
import MessageSection from './MessageSection';
import { PendingVideo } from './PendingVideo';
import Process from './Process';
import Role from './Role';
import SuggestQuestion from './SuggestQuestion';
import { Message } from './types';

interface Props {
  agentInfo?: AgentInfoType;
  message: Message;
  user?: UserData;
  refresh: (chatId: string) => void;
  dislike: (chatId: string, isCancel: boolean) => void;
  like: (chatId: string, isCancel: boolean) => void;
  /** 创建新对话 */
  newChat?: (question: string) => void;
  isSDKMode?: boolean;
  /** 显示 COT 过程 */
  showCOT?: boolean;
  onDebugInfo?: (flowResponseDetail: unknown) => void;
  openImageViewer?: (src: string) => void;
  openImageViewerList?: (urls: string[], currentIndex: number, filename?: string) => void;
  handleArtifactClick?: (chatId: string) => void;
}

const MessageCard: React.FC<Props> = (props) => {
  const {
    message,
    refresh,
    like,
    dislike,
    user,
    newChat,
    agentInfo,
    showCOT,
    openImageViewer,
    openImageViewerList,
    handleArtifactClick,
  } = props;
  const { content, status, sources, process, suggestQuestions } = message;
  const [showActions, setShowActions] = useState<boolean>(false);
  /** 是否是用户 */
  const isMe = message.role === 'user';
  const isPending = status === 'pending';

  const roleName = isMe ? user?.username : 'Kwaipilot';
  /** 是否展示运行过程, 回答时默认展开 */
  const [isProcessOpen, setIsProcessOpen] = useState(false);
  const toggleProcess = useCallback(() => {
    setIsProcessOpen((pre) => !pre);
  }, []);
  const onCopy = useCallback(async () => {
    if (copy(content)) {
      if (!props.isSDKMode) {
        toast.success('复制成功');
      }
    }
  }, [content, props.isSDKMode]);

  const onDislike = useCallback(
    (isCancel: boolean) => {
      dislike(message.chatId, isCancel);
    },
    [dislike, message.chatId],
  );

  const onLike = useCallback(
    (isCancel: boolean) => {
      like(message.chatId, isCancel);
    },
    [like, message.chatId],
  );

  const onRefresh = useCallback(() => {
    refresh(message.chatId);
  }, [message.chatId, refresh]);

  return props.isSDKMode ? (
    <>
      <div
        className={style['message']}
        style={{ width: '100%', marginBottom: 0 }}
        onMouseEnter={() => setShowActions(true)}
        onMouseLeave={() => setShowActions(false)}
      >
        <div className={style['message-card']} style={{ flexDirection: 'column', gap: '8px' }}>
          <div style={{ display: 'flex' }}>
            {status !== 'pending' && (
              <>
                {!agentInfo?.icon && <CommonIcon name="logoIcon" className={style['avatar']} />}
                {agentInfo?.icon && <Avatar src={agentInfo.icon} className={style['avatar']} />}
              </>
            )}
            {status === 'pending' && <PendingVideo />}
            <Role
              agentName={agentInfo?.name}
              isMe={isMe}
              showCOT={showCOT}
              process={process}
              roleName={roleName || ''}
              status={status}
            />
          </div>
          <div className={style['right']} style={{ width: '100%', gap: '2px' }}>
            {message.content && (sources || suggestQuestions?.length) ? (
              <MessageSection title="回答" icon={<CommonIcon name="answerIcon" />}>
                {content && (
                  <div style={{ background: '#fff', borderRadius: '8px' }}>
                    <Content
                      isSDKMode={props.isSDKMode}
                      content={message.content}
                      status={message.status}
                      messageType={message.messageType}
                      openImageViewer={openImageViewer}
                    />
                  </div>
                )}
              </MessageSection>
            ) : !content && isPending ? (
              <div className={style['skeleton-container']}>
                <div className={style['item1']}></div>
                <div className={style['item1']}></div>
                <div className={style['item2']}></div>
                <div className={style['item3']}></div>
              </div>
            ) : (
              <div className={style['section']} style={{ background: '#fff', borderRadius: '8px' }}>
                {content && (
                  <Content
                    className={style['answer-content']}
                    content={message.content}
                    status={message.status}
                    isSDKMode={props.isSDKMode}
                    messageType={message.messageType}
                    openImageViewer={openImageViewer}
                  />
                )}
              </div>
            )}
            {!isPending && (
              <Actions
                onCopy={onCopy}
                onDislike={onDislike}
                onLike={onLike}
                onRefresh={onRefresh}
                isSDKMode={props.isSDKMode}
                showActions={showActions}
              />
            )}
            {!!suggestQuestions?.length && newChat && (
              <MessageSection
                title="猜你想问"
                icon={
                  <CommonIcon
                    name="guessAskIcon"
                    style={{
                      width: '16px',
                      height: '16px',
                      marginRight: '4px',
                    }}
                  />
                }
              >
                <SuggestQuestion questions={suggestQuestions} onClickQuestion={newChat} />
              </MessageSection>
            )}
          </div>
        </div>
      </div>
    </>
  ) : (
    <>
      <div className={style['message']}>
        <div className={style['message-card']}>
          <div className={style['left']}>
            {status !== 'pending' && (
              <>
                {!agentInfo?.icon && <CommonIcon name="logoIcon" className={style['avatar']} />}
                {agentInfo?.icon && <Avatar src={agentInfo.icon} className={style['avatar']} />}
              </>
            )}
            {status === 'pending' && <PendingVideo />}
          </div>
          <div className={style['right']}>
            <Role
              agentName={agentInfo?.name}
              isMe={isMe}
              showCOT={showCOT}
              process={process}
              roleName={roleName || ''}
              status={status}
              toggleProcess={toggleProcess}
              onDebugInfo={props.onDebugInfo}
              workflowResponseDetail={message.workflowResponseDetail}
            />
            {isProcessOpen && !isMe && <Process process={process} status={message.status} />}

            {message.content && (sources || suggestQuestions?.length) ? (
              <MessageSection title="回答" icon={<CommonIcon name="answerIcon" />}>
                {content && (
                  <Content
                    content={message.content}
                    status={message.status}
                    messageType={message.messageType}
                    message={message}
                    handleArtifactClick={handleArtifactClick}
                    openImageViewerList={openImageViewerList}
                  />
                )}
                {!isPending && (
                  <Actions
                    onCopy={onCopy}
                    onDislike={onDislike}
                    onLike={onLike}
                    onRefresh={onRefresh}
                  />
                )}
              </MessageSection>
            ) : !content && isPending ? (
              <div className={style['skeleton-container']}>
                <div className={style['item1']}></div>
                <div className={style['item1']}></div>
                <div className={style['item2']}></div>
                <div className={style['item3']}></div>
              </div>
            ) : (
              <div className={style['section']}>
                {content && (
                  <Content
                    className={style['answer-content']}
                    content={message.content}
                    status={message.status}
                    messageType={message.messageType}
                    message={message}
                    handleArtifactClick={handleArtifactClick}
                    openImageViewerList={openImageViewerList}
                  />
                )}
                {!isPending && (
                  <Actions
                    onCopy={onCopy}
                    onDislike={onDislike}
                    onLike={onLike}
                    onRefresh={onRefresh}
                  />
                )}
              </div>
            )}
            {!!suggestQuestions?.length && newChat && (
              <MessageSection
                title="猜你想问"
                icon={
                  <CommonIcon
                    name="guessAskIcon"
                    style={{
                      width: '16px',
                      height: '16px',
                      marginRight: '4px',
                    }}
                  />
                }
              >
                <SuggestQuestion questions={suggestQuestions} onClickQuestion={newChat} />
              </MessageSection>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default memo(MessageCard);
