.message {
  display: flex;
  position: relative;
  align-items: flex-start;
  // padding-bottom: 32px;
  margin-bottom: 32px;
  width: 826px;
}
.message:last-child {
  // padding-bottom: 32px;
  border-bottom: none;
  // margin-bottom: 0;
}

.message-card {
  display: flex;
  align-items: flex-start;
  overflow: hidden;
  flex: 1 1 auto;
  .avatar {
    width: 28px;
    height: 28px;
    margin-right: 8px;
  }
  .left {
    flex: 0 0 36px;
    height: 28px;
    width: 36px;
  }
  .right {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    overflow: hidden;
    gap: 4px;
    .section {
      width: 100%;
    }
    .answer-content {
      padding: 8px 16px;
      border-radius: 16px;
      border: 1px solid #f0f2f5;
      background: #f9f9f9;
    }
  }
}
.message-related {
  position: sticky;
  top: 0;
  flex: 0 0 auto;
  width: 252px;
  margin-left: 48px;
  overflow: hidden;
}
.pending-anim {
  width: 100%;
  height: 100%;
}
.skeleton-container {
  width: 100%;
  height: 84px;
  padding: 12px;
  border: 0.6px solid var(--skeleton-border-color);
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  gap: 12px;
  .item1 {
    width: 100%;
    height: 6px;
    border-radius: 1px;
    background: var(--skeleton-bg-color);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
  }
  .item2 {
    width: 66%;
    height: 6px;
    border-radius: 1px;
    background: var(--skeleton-bg-color);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
  }
  .item3 {
    width: 33%;
    height: 6px;
    border-radius: 1px;
    background: var(--skeleton-bg-color);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0 50%;
  }
}

:global(.chatFullScreen) .message {
  width: 100%;
}

@media screen and (min-width: 769px) {
  .message {
    // margin: 0 24px;
    max-width: 100%;
  }
}
