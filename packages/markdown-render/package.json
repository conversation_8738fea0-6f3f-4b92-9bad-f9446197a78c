{"name": "@ks-kwaipilot/markdown-render", "private": false, "version": "0.0.1", "main": "src/index.ts", "type": "module", "description": "markdown渲染库", "scripts": {}, "dependencies": {"@ks-kwaipilot/utils": "workspace:*", "bowser": "^2.11.0", "clsx": "^2.1.0", "copy-to-clipboard": "^3.3.3", "katex": "^0.16.10", "less": "^4.2.0", "mdast-util-gfm-footnote": "1.0.2", "mdast-util-gfm-strikethrough": "1.0.3", "mdast-util-gfm-table": "1.0.7", "mdast-util-gfm-task-list-item": "1.0.2", "micromark-extension-gfm-footnote": "1.1.2", "micromark-extension-gfm-strikethrough": "1.0.7", "micromark-extension-gfm-table": "1.0.7", "micromark-extension-gfm-task-list-item": "1.0.5", "micromark-util-combine-extensions": "1.1.0", "react-markdown": "8.0.7", "react-syntax-highlighter": "15.5.0", "rehype-katex": "6.0.3", "remark-breaks": "3.0.2", "remark-math": "5.1.1", "unist-util-visit": "^5.0.0"}, "peerDependencies": {"antd": "^5.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3"}, "devDependencies": {"@types/react": "^18.2.9", "@types/react-dom": "^18.2.9", "@types/react-syntax-highlighter": "^15.5.11", "@types/unist": "^3.0.3"}, "repository": {"type": "git", "url": "*************************:web-infra/ai-devops/kwaipilot-fe.git"}}