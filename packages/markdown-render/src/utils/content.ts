/**
 * 用于在字符串中逃逸美元符号($)后面紧接着是数字的情况
 * 主要是为了防止在解析文本时，$与数字结合被误识别为特殊格式（如Markdown中的货币表示）
 *
 * @param {string} text - 需要处理的原始字符串
 * @returns {string} - 处理后的字符串，其中每个被数字跟随的美元符号都被替换为`\$`
 */
export const escapeDollarNumber = (text: string) => {
  let escapedText = '';
  // 遍历输入字符串的每个字符
  for (let i = 0; i < text.length; i += 1) {
    let char = text[i];
    const nextChar = text[i + 1] || ' ';
    // 如果当前字符是美元符号，并且下一个字符是数字
    if (char === '$' && nextChar >= '0' && nextChar <= '9') {
      // 将美元符号替换为转义的美元符号
      char = '\\$';
    }
    // 添加处理后的字符到结果字符串
    escapedText += char;
  }

  return escapedText;
};

/**
 * 用于在字符串中逃逸Markdown的代码块和特殊括号
 * 它会保留代码块(```...``` 或 `...`)，并将未转义的方括号([])和圆括号()转换为数学公式或强调格式
 *
 * @param {string} text - 需要处理的原始字符串
 * @returns {string} - 处理后的字符串，其中Markdown的代码块保持不变，方括号转换为数学公式，圆括号转换为强调
 */
export const escapeBrackets = (text: string) => {
  // 正则表达式匹配代码块、未转义的方括号和圆括号
  const pattern = /(```[\s\S]*?```|`.*?`)|\\\[([\s\S]*?[^\\])\\\]|\\\((.*?)\\\)/g;
  return text.replace(pattern, (match, codeBlock, squareBracket, roundBracket) => {
    // 如果匹配到的是代码块，直接返回
    if (codeBlock) {
      return codeBlock;
      // 如果匹配到的是未转义的方括号，将其转换为数学公式
    } else if (squareBracket) {
      return `$$${squareBracket}$$`;
    } else if (roundBracket) {
      return `$${roundBracket}$`;
    }
    return match;
  });
};
