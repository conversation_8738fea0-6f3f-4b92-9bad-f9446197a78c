import React, { use<PERSON>allback, useEffect, useMemo } from 'react';
import ReactMarkDown, { Components } from 'react-markdown';
import { PluggableList } from 'react-markdown/lib/react-markdown';

import clsx from 'clsx';
import 'katex/dist/katex.min.css';

import { useCookieContent } from '../../hooks/useCookieContent';
import { useHandleMessageContent } from '../../hooks/useHandleMessageContent';
import {
  ArtifactParse,
  FilterSourceTag,
  SourceTagWithMetaData,
  WebPagePreview,
} from '../../plugins';
import { presetRehypePlugins, presetRemarkPlugins } from '../../preset';
import themeColor from '../../styles/theme.module.less';
import { ArtifactData } from '../../types/artifactParse';
import { Message, MessageSpecialType } from '../../types/message';
import { WebPagePreviewData } from '../../types/webpreview';
import { ArtifactsBlock, Code, Div, ImgPreviewer, Link, Table, WebPagePreviewComp } from '../index';
import style from './index.module.less';

interface IProps {
  content: string;
  components?: Components;
  theme: 'dark' | 'light';
  remarkPlugins?: PluggableList;
  rehypePlugins?: PluggableList;
  className?: string;
}

export const BrowserMarkDown: React.FC<IProps> = (props) => {
  const { content, components = {}, remarkPlugins, rehypePlugins, className } = props;

  const cookieContent = useCookieContent(content);

  useEffect(() => {
    const orderedLists = document.querySelectorAll('ol');

    orderedLists.forEach(function (ol) {
      const startAttr = ol.getAttribute('start');
      if (startAttr !== null) {
        // 如果start属性存在，将其值转换为整数进行设置
        const startValue = parseInt(startAttr, 10) - 1; // 减去1，因为counter-increment将紧接着发生
        ol.style.counterReset = 'item ' + startValue;
      } else {
        ol.style.counterReset = 'item';
      }
    });
  }, []);

  const markDownComponents: Components = useMemo(() => {
    return {
      div: (props) => {
        const { className, children } = props;
        return <Div className={className}>{children}</Div>;
      },
      table: (props) => <Table>{props.children}</Table>,
      ...components,
    };
  }, [components]);

  return (
    <ReactMarkDown
      remarkPlugins={remarkPlugins || presetRemarkPlugins}
      rehypePlugins={rehypePlugins || presetRehypePlugins}
      components={markDownComponents}
      className={className}
    >
      {cookieContent}
    </ReactMarkDown>
  );
};

interface WebMarkDownProps {
  content: string;
  linkClick: (href: string) => void;
  imgPreview: (url: string[], currentIndex: number, filename?: string) => void;
  imgDownload: (url: string, name?: string) => void;
  codeCopy: (content: string) => void;
  copyText?: string;
  components?: Components;
  theme: 'dark' | 'light';
  containerClassName?: string;
  markdownClassName?: string;
  sourceTagMap: Map<number, number>;
}

export const WebMarkDown: React.FC<WebMarkDownProps> = (props) => {
  const {
    linkClick,
    components = {},
    imgDownload,
    imgPreview,
    content,
    codeCopy,
    copyText,
    theme,
    containerClassName,
    markdownClassName,
    sourceTagMap,
  } = props;

  const isDark = theme === 'dark';
  const markDownComponents: Components = useMemo(() => {
    return {
      a: (props) => {
        const { href, children } = props;
        return (
          <Link href={href} handleClick={linkClick}>
            {children}
          </Link>
        );
      },
      img: (props) => {
        let url = props.src || '';
        try {
          const uri = new URL(props.src || '');
          const query = uri.searchParams;
          if (uri.hostname === 'docs.corp.kuaishou.com') {
            url = `${location.origin}/api/kwaipilot/proxy/doc/image?docId=${query.get('docId')}&imageId=${query.get('id')}`;
          }
        } catch (error) {}
        return (
          <ImgPreviewer
            src={url}
            download={imgDownload}
            preview={imgPreview}
            content={content}
            showAction={true}
            theme={theme}
          ></ImgPreviewer>
        );
      },
      code: (props) => {
        const { inline, className, children } = props;
        return (
          <Code handleCopy={codeCopy} inline={inline} className={className} copyText={copyText}>
            {children}
          </Code>
        );
      },
      ...components,
    };
  }, [components, codeCopy, imgDownload, imgPreview, linkClick]);

  return (
    <div
      className={clsx(
        isDark ? themeColor['dark'] : themeColor['light'],
        style['web-markdown'],
        style['web-markdown-path'],
        containerClassName,
      )}
    >
      <BrowserMarkDown
        theme={theme}
        content={content}
        components={markDownComponents}
        className={markdownClassName}
        remarkPlugins={[...presetRemarkPlugins, SourceTagWithMetaData(sourceTagMap)]}
        rehypePlugins={[...presetRehypePlugins, FilterSourceTag]}
      ></BrowserMarkDown>
    </div>
  );
};

interface OpenPlatformMarkDownProps {
  content: string;
  imgPreview?: (url: string) => void;
  codeCopy?: (content: string) => void;
  components?: Components;
  containerStyle?: React.CSSProperties;
  containerClassName?: string;
  markdownClassName?: string;
  messageType?: MessageSpecialType;
  status: Message['status'];
  remarkPlugins?: PluggableList;
  rehypePlugins?: PluggableList;
  handleArtifactClick?: () => void;
  openImageViewerList?: (urls: string[], currentIndex: number, filename?: string) => void;
  viewerImageList?: boolean;
}

export const OpenPlatformMarkDown: React.FC<OpenPlatformMarkDownProps> = (props) => {
  const {
    components = {},
    imgPreview,
    codeCopy,
    content: oriContent,
    messageType,
    status,
    containerStyle = {},
    containerClassName,
    markdownClassName,
    remarkPlugins,
    rehypePlugins,
    viewerImageList,
    openImageViewerList,
  } = props;

  const preview = useCallback(
    (urls: string[], currentIndex: number, filename?: string) => {
      if (viewerImageList) {
        openImageViewerList?.(urls, currentIndex, filename);
      } else {
        imgPreview?.(urls[0]);
      }
    },
    [imgPreview, openImageViewerList, viewerImageList],
  );

  const markDownComponents: Components = useMemo(() => {
    return {
      a: (props) => {
        const { href, children } = props;
        return (
          <a rel="noopener noreferrer" className="react-markdown-link" href={href} target="_blank">
            {children}
          </a>
        );
      },
      img: (props) => (
        <ImgPreviewer
          src={props.src || ''}
          content={oriContent}
          preview={preview}
          showAction={false}
        ></ImgPreviewer>
      ),
      code: (props) => {
        const { inline, className, children } = props;
        return (
          <Code handleCopy={codeCopy} inline={inline} className={className}>
            {children}
          </Code>
        );
      },
      ...components,
    };
  }, [components, preview, codeCopy]);

  const { markdown: content, component } = useHandleMessageContent({
    content: oriContent,
    messageType,
    status,
  });

  if (component) {
    return (
      <div style={containerStyle} className={clsx(style['web-markdown'], containerClassName)}>
        {component}
      </div>
    );
  }

  return (
    <div style={containerStyle} className={clsx(style['web-markdown'], containerClassName)}>
      <BrowserMarkDown
        content={content}
        components={markDownComponents}
        theme="light"
        className={markdownClassName}
        remarkPlugins={remarkPlugins}
        rehypePlugins={rehypePlugins}
      ></BrowserMarkDown>
    </div>
  );
};

export const SDKMarkDown: React.FC<OpenPlatformMarkDownProps> = (props) => {
  return (
    <OpenPlatformMarkDown
      containerStyle={{
        background: 'transparent',
        padding: '12px',
        borderRadius: '8px',
        border: 'none',
      }}
      {...props}
    ></OpenPlatformMarkDown>
  );
};

export const AgentsMarkDown: React.FC<OpenPlatformMarkDownProps> = (props) => {
  const { containerClassName, handleArtifactClick } = props;
  const AgentsClass = clsx(containerClassName, style['web-markdown-path']);

  const webViewPage = {
    webpagepreivew: ({ node }: { node: any }) => {
      const properties = node.properties;
      try {
        const res = JSON.parse(properties.res) as WebPagePreviewData[];
        return <WebPagePreviewComp tabs={res} />;
      } catch (error) {
        return null;
      }
    },
    artifact: ({ data }: { data: string }) => {
      const dataObj: ArtifactData = JSON.parse(
        data || ('{}' as unknown as string),
      ) as unknown as ArtifactData;

      return <ArtifactsBlock title={dataObj.title} handleArtifactClick={handleArtifactClick} />;
    },
  } as Components;

  return (
    <OpenPlatformMarkDown
      {...props}
      viewerImageList={true}
      remarkPlugins={[...presetRemarkPlugins, WebPagePreview, ArtifactParse]}
      containerClassName={AgentsClass}
      components={webViewPage}
    ></OpenPlatformMarkDown>
  );
};
