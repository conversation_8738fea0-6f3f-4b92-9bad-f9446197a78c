import { ReactNode, Suspense, lazy } from 'react';

import style from './index.module.less';

interface IProps {
  handleCopy?: (content: string) => void;
  inline?: boolean;
  className?: string;
  children: ReactNode;
  copyText?: string;
}
// 懒加载 CodeBlock 组件(highlighter依赖太大了)
const CodeBlock = lazy(() => import('./CodeBlock'));

export const Code: React.FC<IProps> = (props) => {
  const { inline, className, children, handleCopy, copyText } = props;
  const match = /language-(\w+)/.exec(className || '');
  if (!inline && match) {
    return (
      <Suspense>
        <CodeBlock
          language={match[1].trim()}
          value={String(children).replace(/\n$/, '').trim()}
          handleCopy={handleCopy}
          copyText={copyText}
        />
      </Suspense>
    );
  }

  return <code className={`${style['simple-code-block']}`}>{children}</code>;
};
