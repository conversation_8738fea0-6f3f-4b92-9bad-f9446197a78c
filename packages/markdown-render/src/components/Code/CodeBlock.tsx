import SyntaxHighlighter from 'react-syntax-highlighter';
import { irBlack } from 'react-syntax-highlighter/dist/esm/styles/hljs';

import CopyIcon from '../../assets/code-copy.svg?react';
import style from './index.module.less';

interface IProps {
  language: string;
  value: string;
  handleCopy?: (content: string) => void;
  copyText?: string;
}

const CodeBlock = (props: IProps) => {
  const { language, value, handleCopy, copyText = '复制' } = props;

  return (
    <div style={{ display: 'flex', flexDirection: 'column' }} className={style['code-block']}>
      <div className={style['header']}>
        {language && <span className={style['language-font']}>{language}</span>}
        <div
          className={style['code-copy-icon']}
          onClick={() => handleCopy?.(value)}
          style={{ color: '#C7CFD7' }}
        >
          <CopyIcon />
          {copyText}
        </div>
      </div>
      <SyntaxHighlighter
        language={language}
        style={irBlack}
        PreTag={(props) => (
          <p
            {...props}
            style={{
              display: 'block',
              overflowX: 'auto',
              padding: '4px 12px',
              marginTop: '0px',
              borderRadius: '0px 0px 8px 8px',
              background: '#0F161B',
              color: 'rgb(248, 248, 248)',
            }}
          />
        )}
      >
        {value}
      </SyntaxHighlighter>
    </div>
  );
};

export default CodeBlock;
