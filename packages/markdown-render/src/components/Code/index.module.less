.simple-code-block {
  color: var(--md-inline-code-color, '#000000');
  background-color: var(--md-inline-code-bg, '#dee0e8');
  padding: 2px 8px;
  margin: 0 4px;
  border-radius: 4px;
  line-height: 26px;
  font-size: 15px;
}

// TODO: 先用以前的，后面再改
.language-font {
  color: var(--block-header-language-color, '#f0f0f0');

  /* font_body */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.code-copy-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--block-header-copy-code-text, '#8b949e');

  svg {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}

.header {
  display: flex;
  padding: 8px 12px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  font-weight: 500;
  font-size: 12px;
  line-height: 18px;
  border-radius: 8px 8px 0 0;
  border-bottom: 0.5px solid var(--md-stroke-border, '#555D67');
  background: var(--code-mask, '#3a3c40');
  // color: var(--text-primary, '#EBEBEB');
}
