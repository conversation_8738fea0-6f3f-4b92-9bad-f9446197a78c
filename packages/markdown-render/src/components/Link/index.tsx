import { ReactNode } from 'react';

import { isEmail } from '../../utils/isEmail';

interface IProps {
  handleClick: (href: string) => void;
  href?: string;
  children: ReactNode;
}

export const Link: React.FC<IProps> = (props) => {
  const { href, handleClick } = props;
  if (!href || isEmail(href)) {
    return <span>{props.children}</span>;
  }

  return (
    <a
      rel="noopener noreferrer"
      className="react-markdown-link"
      data-url={props.href}
      onClick={(e) => {
        e.stopPropagation();
        handleClick?.(href);
      }}
    >
      {props.href}
    </a>
  );
};
