import { Parent } from 'unist';
import { visit } from 'unist-util-visit';


interface KNode {
  type: string;
  value?: string;
}



export const ArtifactParse = () => {
  return (tree: any) => {
    visit(tree, 'html', (node: KNode, _index?: number, parent?: Parent) => {
      const startRegex = /<artifact\b[^>]*>/
      if (startRegex.test(node.value ?? '')) {

        if (parent && parent.children.length) {

          const startIdx = parent.children.findIndex((i: KNode) => {
            if (i.type === 'html' && startRegex.test(i.value ?? '')) {
              return true
            }
            return false
          })

          const endIdx = parent.children.findIndex((i: KNode) => {
            if (i.type === 'html' && i.value === '</artifact>') {
              return true
            }
            return false
          })

          if (startIdx !== -1 && endIdx !== -1 && endIdx - startIdx === 2) {
            const contentNode = parent.children[startIdx + 1] as KNode

            if (contentNode.type === 'text') {
              const title = contentNode.value ?? 'Artifact'

              const data = JSON.stringify({
                title,
              })

              parent.children.splice(startIdx, 3, {
                type: 'artifact',
                data: { hName: 'artifact', hProperties: { data } },
              });
            }

          }
        }
      }
    });
  };
};