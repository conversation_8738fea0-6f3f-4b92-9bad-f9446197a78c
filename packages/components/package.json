{"name": "@kwaipilot/components", "description": "Kwaipilot 公用组件", "private": false, "version": "1.0.0", "main": "src/index.ts", "type": "module", "scripts": {}, "engines": {"node": ">=18", "pnpm": ">=8"}, "peerDependencies": {"antd": "^5.16.4", "react": "^18.2.0", "react-dom": "^18.2.0"}, "dependencies": {"@kid/enterprise-icon": "^1.0.501", "@ks-kwaipilot/hooks": "workspace:*", "@ks-kwaipilot/utils": "workspace:*", "clsx": "^2.1.0", "copy-to-clipboard": "^3.3.3"}, "devDependencies": {"@types/react": "^18.2.9", "@types/react-dom": "^18.2.9"}}