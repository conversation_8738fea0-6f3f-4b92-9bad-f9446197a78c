import { CSSProperties, useCallback } from 'react';

import { Button } from 'antd';
import clsx from 'clsx';

import notFoundDark from '../assets/404-dark.svg';
import notFoundLight from '../assets/404-light.svg';
import { DOT_BG_DARK, DOT_BG_LIGHT } from '../constants';
import css from './NotFound.module.less';

interface Props {
  style?: CSSProperties;
  theme?: 'light' | 'dark';
  title?: string;
  desc?: string;
  btnText?: string;
  onBack?: () => void;
}

export const NotFound = (props: Props) => {
  const { style, onBack, theme = 'light', title, desc, btnText } = props;
  const isDark = theme !== 'light';
  const onClick = useCallback(() => {
    if (onBack) {
      onBack();
      return;
    }
    window.location.href = 'https://kwaipilot.corp.kuaishou.com';
  }, [onBack]);
  const img = isDark ? notFoundDark : notFoundLight;
  const backgroundImage = isDark ? DOT_BG_DARK : DOT_BG_LIGHT;
  return (
    <div className={clsx(css['container'], isDark && css['dark'])} style={style}>
      <div
        className={css['inner']}
        style={{
          backgroundImage: `url(${backgroundImage})`,
        }}
      >
        <img src={img} />
        <h3 className={css['title']}>{title || '页面走丢了'}</h3>
        <p className={css['desc']}>{desc || '链接错误或已失效，请检查网页链接或返回首页'}</p>
        <Button type="primary" onClick={onClick} className={css['btn']}>
          {btnText || '返回首页'}
        </Button>
      </div>
    </div>
  );
};
