<svg width="140" height="141" viewBox="0 0 140 141" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_961_4181)">
<g filter="url(#filter0_d_961_4181)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.0976 10.8086C63.41 10.8086 64.6187 11.5222 65.2527 12.6713L70.4885 22.1618C71.1225 23.311 72.3312 24.0245 73.6436 24.0245L126.271 24.0245C130.252 24.0245 133.478 27.2512 133.478 31.2314V91.6065C133.478 95.6862 130.171 98.9933 126.092 98.9933H13.9085C9.82889 98.9933 6.52173 95.6861 6.52173 91.6065V18.1954C6.52173 14.1158 9.8289 10.8086 13.9085 10.8086H62.0976Z" fill="url(#paint0_linear_961_4181)"/>
</g>
<mask id="mask0_961_4181" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="6" y="30" width="128" height="69">
<path d="M6.52161 30.4453H133.478V91.7846C133.478 95.7648 130.252 98.9914 126.271 98.9914H13.7284C9.74822 98.9914 6.52161 95.7648 6.52161 91.7846V30.4453Z" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_961_4181)">
<rect width="61.5656" height="61.5658" rx="5.51417" transform="matrix(0.8321 0.554626 -0.554619 0.832105 80.3885 31.4336)" fill="url(#paint1_linear_961_4181)"/>
<mask id="mask1_961_4181" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="33" y="43" width="71" height="71">
<rect width="59.0465" height="59.0469" rx="5.25844" transform="matrix(0.963028 0.269403 -0.269398 0.963029 48.2386 42.1289)" fill="white"/>
</mask>
<g mask="url(#mask1_961_4181)">
<rect width="161.722" height="59.0565" rx="5.25844" transform="matrix(0.963028 0.269403 -0.269398 0.963029 48.2408 42.1211)" fill="url(#paint2_linear_961_4181)"/>
<mask id="mask2_961_4181" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="33" y="43" width="170" height="99">
<rect width="161.722" height="59.0565" rx="5.25844" transform="matrix(0.963028 0.269403 -0.269398 0.963029 48.2408 42.1172)" fill="url(#paint3_linear_961_4181)"/>
</mask>
<g mask="url(#mask2_961_4181)">
<rect width="36.4572" height="165.084" transform="matrix(0.718069 0.695975 -0.634641 0.772805 132.533 21.1211)" fill="url(#paint4_linear_961_4181)"/>
<rect width="36.4572" height="130.666" transform="matrix(0.718069 0.695975 -0.634641 0.772805 73.1624 1.12891)" fill="url(#paint5_linear_961_4181)" fill-opacity="0.8"/>
<rect width="36.4572" height="184.633" transform="matrix(0.718069 0.695975 -0.634641 0.772805 136.202 -14.4805)" fill="url(#paint6_linear_961_4181)" fill-opacity="0.2"/>
<rect width="3.87998" height="123.282" transform="matrix(0.718069 0.695975 -0.634641 0.772805 94.6545 32.2109)" fill="url(#paint7_linear_961_4181)"/>
<rect width="3.87998" height="134.093" transform="matrix(0.718069 0.695975 -0.634641 0.772805 105.443 24.9414)" fill="url(#paint8_linear_961_4181)" fill-opacity="0.8"/>
<rect width="3.87998" height="151.225" transform="matrix(0.718069 0.695975 -0.634641 0.772805 120.321 12.832)" fill="url(#paint9_linear_961_4181)" fill-opacity="0.6"/>
<rect width="3.87998" height="130.285" transform="matrix(0.718069 0.695975 -0.634641 0.772805 111.039 30.125)" fill="url(#paint10_linear_961_4181)" fill-opacity="0.8"/>
<rect width="3.87998" height="184.633" transform="matrix(0.718069 0.695975 -0.634641 0.772805 149.307 -10.8203)" fill="url(#paint11_linear_961_4181)" fill-opacity="0.6"/>
<rect width="3.87998" height="184.633" transform="matrix(0.718069 0.695975 -0.634641 0.772805 107.366 -22.5469)" fill="url(#paint12_linear_961_4181)" fill-opacity="0.3"/>
<rect width="0.162514" height="115.184" transform="matrix(0.718069 0.695975 -0.634641 0.772805 61.3274 32.4062)" fill="url(#paint13_linear_961_4181)" fill-opacity="0.3"/>
<rect width="0.162514" height="115.184" transform="matrix(0.718069 0.695975 -0.634641 0.772805 108.253 33.2773)" fill="url(#paint14_linear_961_4181)" fill-opacity="0.8"/>
<rect width="0.162514" height="115.184" transform="matrix(0.718069 0.695975 -0.634641 0.772805 80.2649 25.4453)" fill="url(#paint15_linear_961_4181)" fill-opacity="0.6"/>
<rect width="0.162514" height="115.184" transform="matrix(0.718069 0.695975 -0.634641 0.772805 74.2148 23.7461)" fill="url(#paint16_linear_961_4181)" fill-opacity="0.75"/>
<rect width="3.87998" height="122.127" transform="matrix(0.718069 0.695975 -0.634641 0.772805 73.6506 27.4258)" fill="url(#paint17_linear_961_4181)" fill-opacity="0.5"/>
<rect width="3.87998" height="165.899" transform="matrix(0.718069 0.695975 -0.634641 0.772805 141.255 4.75391)" fill="url(#paint18_linear_961_4181)" fill-opacity="0.8"/>
<rect width="3.87998" height="184.633" transform="matrix(0.718069 0.695975 -0.634641 0.772805 156.992 -8.66016)" fill="url(#paint19_linear_961_4181)" fill-opacity="0.8"/>
<rect width="3.87998" height="184.633" transform="matrix(0.718069 0.695975 -0.634641 0.772805 160.917 -7.57422)" fill="url(#paint20_linear_961_4181)" fill-opacity="0.8"/>
<rect width="3.87998" height="152.277" transform="matrix(0.718069 0.695975 -0.634641 0.772805 143.572 18.332)" fill="url(#paint21_linear_961_4181)" fill-opacity="0.75"/>
<rect width="3.87998" height="184.633" transform="matrix(0.718069 0.695975 -0.634641 0.772805 167.777 -5.64844)" fill="url(#paint22_linear_961_4181)" fill-opacity="0.6"/>
<rect width="0.162514" height="115.184" transform="matrix(0.718069 0.695975 -0.634641 0.772805 125.61 40.1641)" fill="url(#paint23_linear_961_4181)" fill-opacity="0.4"/>
<rect width="0.162514" height="115.184" transform="matrix(0.718069 0.695975 -0.634641 0.772805 130.534 39.5117)" fill="url(#paint24_linear_961_4181)" fill-opacity="0.9"/>
</g>
</g>
<g filter="url(#filter1_b_961_4181)">
<rect width="71.7171" height="58.9754" rx="5.51417" transform="matrix(0.98416 -0.177284 0.177281 0.98416 1.35071 69.4062)" fill="#1E5CB7" fill-opacity="0.23"/>
<rect x="0.193984" y="0.134765" width="71.383" height="58.6413" rx="5.34715" transform="matrix(0.98416 -0.177284 0.177281 0.98416 1.32989 69.4428)" stroke="#194588" stroke-width="0.33404"/>
</g>
</g>
<ellipse cx="17.4252" cy="20.2911" rx="1.30667" ry="1.30668" fill="#00A0E1"/>
<ellipse cx="22.9474" cy="20.2911" rx="1.30667" ry="1.30668" fill="#00AFC7"/>
<ellipse cx="28.4696" cy="20.2911" rx="1.30667" ry="1.30668" fill="#4B8EBF"/>
<ellipse cx="126.147" cy="31.4317" rx="1.30667" ry="1.30668" fill="#00AFC7"/>
<rect width="3.70743" height="8.4338" rx="0.31888" transform="matrix(0.868816 -0.495135 0.495128 0.86882 92.062 109.766)" fill="url(#paint25_linear_961_4181)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M94.4737 110.425C106.407 103.624 110.568 88.4366 103.767 76.5031C96.9665 64.5695 81.7794 60.4086 69.846 67.2095C57.9126 74.0103 53.7517 89.1975 60.5525 101.131C67.3533 113.065 82.5403 117.225 94.4737 110.425ZM90.6486 103.709C98.8755 99.021 101.744 88.551 97.0556 80.324C92.3672 72.0971 81.8972 69.2286 73.6704 73.917C65.4435 78.6055 62.575 89.0755 67.2634 97.3025C71.9519 105.529 82.4218 108.398 90.6486 103.709Z" fill="url(#paint26_linear_961_4181)"/>
<g filter="url(#filter2_b_961_4181)">
<ellipse cx="17.2818" cy="17.2819" rx="17.2818" ry="17.2819" transform="matrix(0.868816 -0.495135 0.495128 0.86882 58.5873 82.3594)" fill="white" fill-opacity="0.23"/>
<path d="M96.8211 80.4614C101.436 88.5593 98.6125 98.865 90.5147 103.48C82.4169 108.095 72.1113 105.271 67.4964 97.1734C62.8816 89.0756 65.705 78.7698 73.8028 74.1549C81.9006 69.54 92.2063 72.3635 96.8211 80.4614Z" stroke="#609BD5" stroke-width="0.81117"/>
</g>
<path d="M93.3618 116.426C92.9638 115.776 93.1825 114.926 93.8447 114.549L98.4897 111.901C99.1519 111.524 99.995 111.769 100.351 112.443L107.117 125.232C108.019 126.936 107.404 129.048 105.729 130.003C104.054 130.958 101.923 130.41 100.916 128.765L93.3618 116.426Z" fill="url(#paint27_linear_961_4181)"/>
</g>
<defs>
<filter id="filter0_d_961_4181" x="-0.325917" y="5.33048" width="140.652" height="101.879" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.36953"/>
<feGaussianBlur stdDeviation="3.42382"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.32549 0 0 0 0 0.705882 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_961_4181"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_961_4181" result="shape"/>
</filter>
<filter id="filter1_b_961_4181" x="-3.77274" y="51.5693" width="91.2831" height="81.002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="3.00636"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_961_4181"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_961_4181" result="shape"/>
</filter>
<filter id="filter2_b_961_4181" x="60.8182" y="67.4754" width="42.6813" height="42.682" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2.02792"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_961_4181"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_961_4181" result="shape"/>
</filter>
<linearGradient id="paint0_linear_961_4181" x1="68.3368" y1="30.6013" x2="68.3368" y2="76.8473" gradientUnits="userSpaceOnUse">
<stop stop-color="#002E5C"/>
<stop offset="1" stop-color="#002142"/>
</linearGradient>
<linearGradient id="paint1_linear_961_4181" x1="13.3238" y1="4.58903" x2="57.2192" y2="31.8877" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E62A5"/>
<stop offset="1" stop-color="#1A538B"/>
</linearGradient>
<linearGradient id="paint2_linear_961_4181" x1="9.24647" y1="17.695" x2="66.31" y2="45.9147" gradientUnits="userSpaceOnUse">
<stop stop-color="#147BF3"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint3_linear_961_4181" x1="9.24647" y1="17.695" x2="151.507" y2="29.481" gradientUnits="userSpaceOnUse">
<stop stop-color="#1448F3"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint4_linear_961_4181" x1="8.27245" y1="41.1893" x2="19.575" y2="164.961" gradientUnits="userSpaceOnUse">
<stop stop-color="#0AE4EE" stop-opacity="0"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint5_linear_961_4181" x1="17.1657" y1="1.34428" x2="20.4269" y2="116.034" gradientUnits="userSpaceOnUse">
<stop stop-color="#154BF4" stop-opacity="0"/>
<stop offset="0.349459" stop-color="#154BF4" stop-opacity="0.5"/>
<stop offset="0.637743" stop-color="#154BF4" stop-opacity="0.4"/>
<stop offset="1" stop-color="#0FE0F1"/>
</linearGradient>
<linearGradient id="paint6_linear_961_4181" x1="-12.5246" y1="25.2362" x2="40.3475" y2="142.705" gradientUnits="userSpaceOnUse">
<stop stop-color="#001AFF" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint7_linear_961_4181" x1="-1.33294" y1="16.8505" x2="29.3191" y2="28.1096" gradientUnits="userSpaceOnUse">
<stop stop-color="#001AFF" stop-opacity="0"/>
<stop offset="0.612337" stop-color="#35ADEE"/>
<stop offset="1" stop-color="#00F0FF"/>
</linearGradient>
<linearGradient id="paint8_linear_961_4181" x1="-1.33294" y1="18.3282" x2="28.8685" y2="28.161" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint9_linear_961_4181" x1="3.73658" y1="0.140683" x2="40.4226" y2="12.8394" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint10_linear_961_4181" x1="-1.33294" y1="17.8078" x2="28.6979" y2="27.8707" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint11_linear_961_4181" x1="2.54009" y1="0.166132" x2="40.462" y2="10.5165" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint12_linear_961_4181" x1="2.54009" y1="0.166132" x2="40.462" y2="10.5165" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint13_linear_961_4181" x1="0.106392" y1="0.103643" x2="1.81252" y2="0.134907" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint14_linear_961_4181" x1="0.106392" y1="0.103643" x2="1.81252" y2="0.134907" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.279251" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint15_linear_961_4181" x1="0.106392" y1="0.103643" x2="1.81252" y2="0.134907" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint16_linear_961_4181" x1="0.106392" y1="0.103643" x2="1.81252" y2="0.134907" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint17_linear_961_4181" x1="2.54009" y1="0.10989" x2="37.3586" y2="14.4772" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint18_linear_961_4181" x1="-1.33294" y1="22.6756" x2="29.9065" y2="30.8964" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint19_linear_961_4181" x1="-1.33294" y1="25.2362" x2="30.3012" y2="32.7162" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint20_linear_961_4181" x1="-1.33294" y1="25.2362" x2="30.3012" y2="32.7162" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint21_linear_961_4181" x1="-1.33294" y1="20.8137" x2="29.5329" y2="29.6628" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint22_linear_961_4181" x1="-1.33294" y1="25.2362" x2="30.3012" y2="32.7162" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint23_linear_961_4181" x1="0.106392" y1="0.103643" x2="1.81252" y2="0.134907" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint24_linear_961_4181" x1="0.106392" y1="0.103643" x2="1.81252" y2="0.134907" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint25_linear_961_4181" x1="1.8543" y1="0.36891" x2="4.39169" y2="3.26234" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7FAFF"/>
<stop offset="1" stop-color="#91BDFF"/>
</linearGradient>
<linearGradient id="paint26_linear_961_4181" x1="54.2835" y1="54.8955" x2="111.9" y2="86.4142" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7FAFF"/>
<stop offset="1" stop-color="#91BDFF"/>
</linearGradient>
<linearGradient id="paint27_linear_961_4181" x1="92.6344" y1="106.491" x2="109.734" y2="111.856" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7FAFF"/>
<stop offset="1" stop-color="#91BDFF"/>
</linearGradient>
<clipPath id="clip0_961_4181">
<rect width="140" height="140" fill="white" transform="translate(0 0.804688)"/>
</clipPath>
</defs>
</svg>
