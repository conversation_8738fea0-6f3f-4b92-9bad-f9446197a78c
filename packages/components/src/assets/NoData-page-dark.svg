<svg width="141" height="141" viewBox="0 0 141 141" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1028_8107)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M50.1815 20.1914C51.1548 20.1914 52.0512 20.7206 52.5213 21.5728L56.4043 28.611C56.8745 29.4632 57.7708 29.9924 58.7441 29.9924L115.607 29.9924C118.559 29.9924 120.952 32.3853 120.952 35.3371V115.234C120.952 118.26 118.499 120.712 115.473 120.712H24.3347C23.1524 120.712 21.9638 120.625 20.8183 120.332C11.3863 117.92 8.96582 111.147 8.96582 99.397V25.6695C8.96582 22.644 11.4185 20.1914 14.4439 20.1914H50.1815Z" fill="url(#paint0_linear_1028_8107)"/>
</g>
<rect x="17.4375" y="72.7109" width="39.2531" height="3.44617" rx="1.72309" fill="#0F4173"/>
<rect x="24.0713" y="81.5039" width="32.6194" height="3.44617" rx="1.72309" fill="#0F4173"/>
<rect x="15.1919" y="90.2969" width="32.6194" height="3.44617" rx="1.72309" fill="#0F4173"/>
<circle cx="17.6184" cy="28.2957" r="1.3074" fill="#00A0E1"/>
<circle cx="23.1394" cy="28.2957" r="1.3074" fill="#00AFC7"/>
<circle cx="28.6604" cy="28.2957" r="1.3074" fill="#4B8EBF"/>
<circle cx="115.186" cy="36.4441" r="1.3074" fill="#00A0E1"/>
<mask id="mask0_1028_8107" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="30" y="24" width="77" height="78">
<rect x="28.9697" y="47.2578" width="60.5854" height="60.5859" rx="5" transform="rotate(-23.26 28.9697 47.2578)" fill="url(#paint1_linear_1028_8107)"/>
</mask>
<g mask="url(#mask0_1028_8107)">
<mask id="mask1_1028_8107" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="30" y="25" width="77" height="77">
<rect width="60.5853" height="60.5855" rx="5.56816" transform="matrix(0.918722 -0.394904 0.394902 0.918723 28.9697 47.2578)" fill="white"/>
</mask>
<g mask="url(#mask1_1028_8107)">
<rect width="165.937" height="60.5953" rx="5.56816" transform="matrix(0.918722 -0.394904 0.394902 0.918723 28.9648 47.2422)" fill="url(#paint2_linear_1028_8107)"/>
<mask id="mask2_1028_8107" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="30" y="-17" width="174" height="119">
<rect width="165.937" height="60.5953" rx="5.56816" transform="matrix(0.918722 -0.394904 0.394902 0.918723 28.9644 47.2422)" fill="url(#paint3_linear_1028_8107)"/>
</mask>
<g mask="url(#mask2_1028_8107)">
<rect width="37.4073" height="169.386" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 82.7598 -23.8242)" fill="url(#paint4_linear_1028_8107)"/>
<rect width="37.4073" height="134.07" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 22.4639 -1.54688)" fill="url(#paint5_linear_1028_8107)" fill-opacity="0.8"/>
<rect width="37.4073" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 62.7554 -54.6211)" fill="url(#paint6_linear_1028_8107)" fill-opacity="0.2"/>
<rect width="3.9811" height="126.494" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 59.6494 9.43359)" fill="url(#paint7_linear_1028_8107)"/>
<rect width="3.9811" height="137.587" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 63.5854 -3.31641)" fill="url(#paint8_linear_1028_8107)" fill-opacity="0.8"/>
<rect width="3.9811" height="155.165" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 67.6641 -22.582)" fill="url(#paint9_linear_1028_8107)" fill-opacity="0.6"/>
<rect width="3.9811" height="133.68" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 71.394 -2.78906)" fill="url(#paint10_linear_1028_8107)" fill-opacity="0.8"/>
<rect width="3.9811" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 75.5825 -60.1367)" fill="url(#paint11_linear_1028_8107)" fill-opacity="0.6"/>
<rect width="3.9811" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 34.5288 -42.4844)" fill="url(#paint12_linear_1028_8107)" fill-opacity="0.3"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 33.165 31.0625)" fill="url(#paint13_linear_1028_8107)" fill-opacity="0.3"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 71.1943 1.51953)" fill="url(#paint14_linear_1028_8107)" fill-opacity="0.8"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 43.7993 13.2969)" fill="url(#paint15_linear_1028_8107)" fill-opacity="0.6"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 37.8765 15.8438)" fill="url(#paint16_linear_1028_8107)" fill-opacity="0.75"/>
<rect width="3.9811" height="125.31" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 39.793 19.1406)" fill="url(#paint17_linear_1028_8107)" fill-opacity="0.5"/>
<rect width="3.9811" height="170.222" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 79.1816 -42.5156)" fill="url(#paint18_linear_1028_8107)" fill-opacity="0.8"/>
<rect width="3.9811" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 83.106 -63.375)" fill="url(#paint19_linear_1028_8107)" fill-opacity="0.8"/>
<rect width="3.9811" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 86.9478 -65.0195)" fill="url(#paint20_linear_1028_8107)" fill-opacity="0.8"/>
<rect width="3.9811" height="156.245" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 89.7783 -33.1602)" fill="url(#paint21_linear_1028_8107)" fill-opacity="0.75"/>
<rect width="3.9811" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 93.6626 -67.9102)" fill="url(#paint22_linear_1028_8107)" fill-opacity="0.6"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 89.4971 -4.15625)" fill="url(#paint23_linear_1028_8107)" fill-opacity="0.4"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 93.0054 -7.85156)" fill="url(#paint24_linear_1028_8107)" fill-opacity="0.9"/>
</g>
</g>
</g>
<g filter="url(#filter1_b_1028_8107)">
<path d="M125.029 51.9883H47.4354C44.4836 51.9883 42.0907 54.3812 42.0907 57.3329V105.444C42.0907 111.892 40.5667 114.819 37.9896 117.397C34.5356 120.851 28.586 121.534 24.4473 121.534H125.029C127.981 121.534 130.373 119.141 130.373 116.19V57.3329C130.373 54.3812 127.981 51.9883 125.029 51.9883Z" fill="#1E5CB7" fill-opacity="0.23"/>
<path d="M125.029 121.367H29.2414C29.9966 121.255 30.7599 121.11 31.5153 120.923C33.9682 120.316 36.3513 119.271 38.1077 117.515C39.4083 116.214 40.4492 114.819 41.1628 112.931C41.8754 111.046 42.2577 108.68 42.2577 105.444V57.3329C42.2577 54.4734 44.5758 52.1553 47.4354 52.1553H125.029C127.888 52.1553 130.206 54.4734 130.206 57.3329V116.19C130.206 119.049 127.888 121.367 125.029 121.367Z" stroke="#194588" stroke-width="0.33404"/>
</g>
<path d="M75.3282 52.1406L75.3282 108.102L75.2316 109.119C74.7019 114.699 71.0362 119.487 65.7886 121.455V121.455" stroke="#194588" stroke-width="0.33404"/>
<circle cx="102.512" cy="110.721" r="17.8812" fill="url(#paint25_linear_1028_8107)"/>
<mask id="mask3_1028_8107" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="84" y="92" width="37" height="37">
<circle cx="102.512" cy="110.721" r="17.8812" fill="#FF5D84"/>
</mask>
<g mask="url(#mask3_1028_8107)">
<path d="M95.5468 121.947C88.915 122.776 85.5846 117.75 85.0816 115.185L82.9764 103.134C82.9665 103.077 82.9788 103.018 83.0108 102.97L97.7503 80.8002C97.8104 80.7099 97.9261 80.6751 98.026 80.7173L129.22 93.8949C129.332 93.9424 129.387 94.0706 129.343 94.1843L119.239 120.236C119.157 120.449 118.835 120.419 118.781 120.197C117.647 115.583 114.094 113.512 109.137 114.338C103.263 115.317 103.203 120.99 95.5468 121.947Z" fill="url(#paint26_linear_1028_8107)"/>
<path d="M93.3787 108.511C88.2132 104.448 88.222 97.1993 92.3724 92.7892C99.3416 85.384 111.2 86.9212 116.943 90.8379C127.952 98.3459 127.938 117.504 116.724 124.589C116.55 124.699 116.338 124.528 116.396 124.331C119.115 115.198 116.553 112.381 112.512 110.8C105.775 108.167 100.488 114.103 93.3787 108.511Z" fill="url(#paint27_linear_1028_8107)"/>
</g>
<g filter="url(#filter2_d_1028_8107)">
<path d="M107.455 100.465H102.886C102.603 100.465 102.36 100.666 102.307 100.945L99.6553 115.029C99.5871 115.392 99.865 115.727 100.234 115.727H103.636C103.902 115.727 104.134 115.549 104.204 115.292L108.023 101.208C108.125 100.833 107.843 100.465 107.455 100.465Z" fill="#04488C"/>
<path d="M103.023 117.703H99.548C99.2694 117.703 99.029 117.899 98.9719 118.171L98.5318 120.273C98.4552 120.639 98.7343 120.983 99.1079 120.983H102.546C102.821 120.983 103.06 120.792 103.12 120.524L103.597 118.422C103.681 118.054 103.401 117.703 103.023 117.703Z" fill="#04488C"/>
</g>
<defs>
<filter id="filter0_d_1028_8107" x="2.11818" y="14.7133" width="125.681" height="114.215" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.36953"/>
<feGaussianBlur stdDeviation="3.42382"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.32549 0 0 0 0 0.705882 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1028_8107"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1028_8107" result="shape"/>
</filter>
<filter id="filter1_b_1028_8107" x="18.4345" y="45.9756" width="117.952" height="81.5723" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="3.00636"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1028_8107"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1028_8107" result="shape"/>
</filter>
<filter id="filter2_d_1028_8107" x="93.81" y="100.465" width="18.9435" height="29.9376" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.70904"/>
<feGaussianBlur stdDeviation="2.35452"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.278825 0 0 0 0 0.0109642 0 0 0 0 0.0109642 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1028_8107"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1028_8107" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1028_8107" x1="63.4916" y1="42.7529" x2="63.4916" y2="95.4682" gradientUnits="userSpaceOnUse">
<stop stop-color="#002E5C"/>
<stop offset="1" stop-color="#002142"/>
</linearGradient>
<linearGradient id="paint1_linear_1028_8107" x1="41.7408" y1="63.5424" x2="46.8042" y2="110.386" gradientUnits="userSpaceOnUse">
<stop stop-color="#D9D9D9"/>
<stop offset="1" stop-color="#D9D9D9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1028_8107" x1="9.48745" y1="18.156" x2="68.0381" y2="47.1114" gradientUnits="userSpaceOnUse">
<stop stop-color="#147BF3"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint3_linear_1028_8107" x1="9.48745" y1="18.156" x2="155.456" y2="30.2493" gradientUnits="userSpaceOnUse">
<stop stop-color="#1448F3"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint4_linear_1028_8107" x1="8.48803" y1="42.2625" x2="20.0851" y2="169.26" gradientUnits="userSpaceOnUse">
<stop stop-color="#0AE4EE" stop-opacity="0"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint5_linear_1028_8107" x1="17.6131" y1="1.37931" x2="20.9592" y2="119.058" gradientUnits="userSpaceOnUse">
<stop stop-color="#154BF4" stop-opacity="0"/>
<stop offset="0.349459" stop-color="#154BF4" stop-opacity="0.5"/>
<stop offset="0.637743" stop-color="#154BF4" stop-opacity="0.4"/>
<stop offset="1" stop-color="#0FE0F1"/>
</linearGradient>
<linearGradient id="paint6_linear_1028_8107" x1="-12.851" y1="25.8938" x2="41.3987" y2="146.424" gradientUnits="userSpaceOnUse">
<stop stop-color="#001AFF" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint7_linear_1028_8107" x1="-1.36767" y1="17.2896" x2="30.0832" y2="28.8421" gradientUnits="userSpaceOnUse">
<stop stop-color="#001AFF" stop-opacity="0"/>
<stop offset="0.612337" stop-color="#35ADEE"/>
<stop offset="1" stop-color="#00F0FF"/>
</linearGradient>
<linearGradient id="paint8_linear_1028_8107" x1="-1.36767" y1="18.8057" x2="29.6209" y2="28.8948" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint9_linear_1028_8107" x1="3.83396" y1="0.144349" x2="41.476" y2="13.174" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint10_linear_1028_8107" x1="-1.36767" y1="18.2718" x2="29.4457" y2="28.597" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint11_linear_1028_8107" x1="2.60628" y1="0.170461" x2="41.5164" y2="10.7906" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint12_linear_1028_8107" x1="2.60628" y1="0.170461" x2="41.5164" y2="10.7906" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint13_linear_1028_8107" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint14_linear_1028_8107" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.279251" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint15_linear_1028_8107" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint16_linear_1028_8107" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint17_linear_1028_8107" x1="2.60628" y1="0.112753" x2="38.3322" y2="14.8545" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint18_linear_1028_8107" x1="-1.36767" y1="23.2664" x2="30.6859" y2="31.7015" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint19_linear_1028_8107" x1="-1.36767" y1="25.8938" x2="31.0908" y2="33.5687" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint20_linear_1028_8107" x1="-1.36767" y1="25.8938" x2="31.0908" y2="33.5687" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint21_linear_1028_8107" x1="-1.36767" y1="21.356" x2="30.3025" y2="30.4357" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint22_linear_1028_8107" x1="-1.36767" y1="25.8938" x2="31.0908" y2="33.5687" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint23_linear_1028_8107" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint24_linear_1028_8107" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint25_linear_1028_8107" x1="102.512" y1="115.136" x2="109.479" y2="128.602" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7FAFF"/>
<stop offset="1" stop-color="#91BDFF"/>
</linearGradient>
<linearGradient id="paint26_linear_1028_8107" x1="115.633" y1="114.167" x2="95.3625" y2="101.993" gradientUnits="userSpaceOnUse">
<stop stop-color="#86B8FF"/>
<stop offset="1" stop-color="#BED8FF"/>
</linearGradient>
<linearGradient id="paint27_linear_1028_8107" x1="114.792" y1="116.911" x2="105.244" y2="91.2946" gradientUnits="userSpaceOnUse">
<stop stop-color="#5897EF"/>
<stop offset="1" stop-color="#9BC4FF"/>
</linearGradient>
</defs>
</svg>
