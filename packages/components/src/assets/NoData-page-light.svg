<svg width="141" height="140" viewBox="0 0 141 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1028_8037)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M50.1815 19.7383C51.1548 19.7383 52.0512 20.2675 52.5213 21.1197L56.4043 28.1579C56.8745 29.0101 57.7708 29.5393 58.7441 29.5393L115.607 29.5393C118.559 29.5393 120.952 31.9322 120.952 34.884V114.781C120.952 117.807 118.499 120.259 115.473 120.259H24.3347C23.1524 120.259 21.9638 120.171 20.8183 119.878C11.3863 117.466 8.96582 110.694 8.96582 98.9439V25.2164C8.96582 22.1909 11.4185 19.7383 14.4439 19.7383H50.1815Z" fill="url(#paint0_linear_1028_8037)"/>
</g>
<rect x="17.4375" y="72.2578" width="39.2531" height="3.44617" rx="1.72309" fill="#CDDFED"/>
<rect x="24.0713" y="81.0508" width="32.6194" height="3.44617" rx="1.72309" fill="#CDDFED"/>
<rect x="15.1919" y="89.8438" width="32.6194" height="3.44617" rx="1.72309" fill="#CDDFED"/>
<circle cx="17.6184" cy="27.8426" r="1.3074" fill="#197EF4"/>
<circle cx="23.1394" cy="27.8426" r="1.3074" fill="#1FE2FC"/>
<circle cx="28.6604" cy="27.8426" r="1.3074" fill="#CDDFED"/>
<circle cx="115.186" cy="35.991" r="1.3074" fill="#197EF4"/>
<mask id="mask0_1028_8037" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="30" y="24" width="77" height="77">
<rect x="28.9697" y="46.8047" width="60.5854" height="60.5859" rx="5" transform="rotate(-23.26 28.9697 46.8047)" fill="url(#paint1_linear_1028_8037)"/>
</mask>
<g mask="url(#mask0_1028_8037)">
<mask id="mask1_1028_8037" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="30" y="24" width="77" height="77">
<rect width="60.5853" height="60.5855" rx="5.56816" transform="matrix(0.918722 -0.394904 0.394902 0.918723 28.9697 46.8047)" fill="white"/>
</mask>
<g mask="url(#mask1_1028_8037)">
<rect width="165.937" height="60.5953" rx="5.56816" transform="matrix(0.918722 -0.394904 0.394902 0.918723 28.9648 46.7891)" fill="url(#paint2_linear_1028_8037)"/>
<mask id="mask2_1028_8037" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="30" y="-17" width="174" height="118">
<rect width="165.937" height="60.5953" rx="5.56816" transform="matrix(0.918722 -0.394904 0.394902 0.918723 28.9644 46.7891)" fill="url(#paint3_linear_1028_8037)"/>
</mask>
<g mask="url(#mask2_1028_8037)">
<rect width="37.4073" height="169.386" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 82.7598 -24.2773)" fill="url(#paint4_linear_1028_8037)"/>
<rect width="37.4073" height="134.07" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 22.4639 -2)" fill="url(#paint5_linear_1028_8037)" fill-opacity="0.8"/>
<rect width="37.4073" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 62.7554 -55.0742)" fill="url(#paint6_linear_1028_8037)" fill-opacity="0.2"/>
<rect width="3.9811" height="126.494" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 59.6494 8.98047)" fill="url(#paint7_linear_1028_8037)"/>
<rect width="3.9811" height="137.587" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 63.5854 -3.76953)" fill="url(#paint8_linear_1028_8037)" fill-opacity="0.8"/>
<rect width="3.9811" height="155.165" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 67.6641 -23.0352)" fill="url(#paint9_linear_1028_8037)" fill-opacity="0.6"/>
<rect width="3.9811" height="133.68" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 71.394 -3.24219)" fill="url(#paint10_linear_1028_8037)" fill-opacity="0.8"/>
<rect width="3.9811" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 75.5825 -60.5898)" fill="url(#paint11_linear_1028_8037)" fill-opacity="0.6"/>
<rect width="3.9811" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 34.5288 -42.9375)" fill="url(#paint12_linear_1028_8037)" fill-opacity="0.3"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 33.165 30.6094)" fill="url(#paint13_linear_1028_8037)" fill-opacity="0.3"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 71.1943 1.06641)" fill="url(#paint14_linear_1028_8037)" fill-opacity="0.8"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 43.7993 12.8438)" fill="url(#paint15_linear_1028_8037)" fill-opacity="0.6"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 37.8765 15.3906)" fill="url(#paint16_linear_1028_8037)" fill-opacity="0.75"/>
<rect width="3.9811" height="125.31" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 39.793 18.6875)" fill="url(#paint17_linear_1028_8037)" fill-opacity="0.5"/>
<rect width="3.9811" height="170.222" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 79.1816 -42.9688)" fill="url(#paint18_linear_1028_8037)" fill-opacity="0.8"/>
<rect width="3.9811" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 83.106 -63.8281)" fill="url(#paint19_linear_1028_8037)" fill-opacity="0.8"/>
<rect width="3.9811" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 86.9478 -65.4727)" fill="url(#paint20_linear_1028_8037)" fill-opacity="0.8"/>
<rect width="3.9811" height="156.245" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 89.7783 -33.6133)" fill="url(#paint21_linear_1028_8037)" fill-opacity="0.75"/>
<rect width="3.9811" height="189.444" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 93.6626 -68.3633)" fill="url(#paint22_linear_1028_8037)" fill-opacity="0.6"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 89.4971 -4.60938)" fill="url(#paint23_linear_1028_8037)" fill-opacity="0.4"/>
<rect width="0.166749" height="118.186" transform="matrix(0.995858 0.090913 -0.00881806 0.999962 93.0054 -8.30469)" fill="url(#paint24_linear_1028_8037)" fill-opacity="0.9"/>
</g>
</g>
</g>
<g filter="url(#filter1_b_1028_8037)">
<path d="M125.029 51.5352H47.4354C44.4836 51.5352 42.0907 53.928 42.0907 56.8798V104.991C42.0907 111.439 40.5667 114.366 37.9896 116.943C34.5356 120.397 28.586 121.081 24.4473 121.081H125.029C127.981 121.081 130.373 118.688 130.373 115.737V56.8798C130.373 53.928 127.981 51.5352 125.029 51.5352Z" fill="white" fill-opacity="0.23"/>
<path d="M125.029 120.914H29.2414C29.9966 120.802 30.7599 120.657 31.5153 120.47C33.9682 119.863 36.3513 118.818 38.1077 117.062C39.4083 115.761 40.4492 114.366 41.1628 112.478C41.8754 110.593 42.2577 108.227 42.2577 104.991V56.8798C42.2577 54.0203 44.5758 51.7022 47.4354 51.7022H125.029C127.888 51.7022 130.206 54.0203 130.206 56.8798V115.737C130.206 118.596 127.888 120.914 125.029 120.914Z" stroke="#CDDFED" stroke-width="0.33404"/>
</g>
<path d="M75.3282 51.6875L75.3282 107.649L75.2316 108.666C74.7019 114.246 71.0362 119.034 65.7886 121.002V121.002" stroke="#CDDFED" stroke-width="0.33404"/>
<circle cx="102.512" cy="110.268" r="17.8812" fill="url(#paint25_linear_1028_8037)"/>
<mask id="mask3_1028_8037" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="84" y="92" width="37" height="37">
<circle cx="102.512" cy="110.268" r="17.8812" fill="url(#paint26_linear_1028_8037)"/>
</mask>
<g mask="url(#mask3_1028_8037)">
<path d="M95.5468 121.494C88.915 122.323 85.5846 117.297 85.0816 114.732L82.983 102.718C82.9688 102.637 82.9864 102.553 83.032 102.485L97.7064 80.4131C97.7919 80.2845 97.9568 80.2349 98.0991 80.295L129.134 93.4056C129.294 93.4733 129.372 93.6558 129.309 93.8178L119.364 119.463C119.246 119.766 118.781 119.725 118.694 119.412C117.474 115.031 113.974 113.079 109.137 113.885C103.263 114.864 103.203 120.537 95.5468 121.494Z" fill="url(#paint27_linear_1028_8037)"/>
<path d="M93.3787 108.058C88.2132 103.995 88.222 96.7462 92.3724 92.3361C99.3416 84.9309 111.2 86.468 116.943 90.3848C127.884 97.8462 127.938 116.814 116.932 124.003C116.686 124.163 116.383 123.926 116.465 123.644C119.071 114.697 116.519 111.914 112.512 110.347C105.775 107.714 100.488 113.65 93.3787 108.058Z" fill="url(#paint28_linear_1028_8037)"/>
</g>
<g filter="url(#filter2_d_1028_8037)">
<path d="M107.455 100.012H102.886C102.603 100.012 102.36 100.213 102.307 100.491L99.6553 114.576C99.5871 114.939 99.865 115.274 100.234 115.274H103.636C103.902 115.274 104.134 115.096 104.204 114.839L108.023 100.754C108.125 100.38 107.843 100.012 107.455 100.012Z" fill="white"/>
<path d="M103.023 117.25H99.548C99.2694 117.25 99.029 117.445 98.9719 117.718L98.5318 119.82C98.4552 120.186 98.7343 120.529 99.1079 120.529H102.546C102.821 120.529 103.06 120.339 103.12 120.071L103.597 117.969C103.681 117.601 103.401 117.25 103.023 117.25Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_1028_8037" x="2.11818" y="14.2602" width="125.681" height="114.215" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.36953"/>
<feGaussianBlur stdDeviation="3.42382"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.694118 0 0 0 0 0.752941 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1028_8037"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1028_8037" result="shape"/>
</filter>
<filter id="filter1_b_1028_8037" x="18.4345" y="45.5224" width="117.952" height="81.5723" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="3.00636"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1028_8037"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1028_8037" result="shape"/>
</filter>
<filter id="filter2_d_1028_8037" x="93.81" y="100.012" width="18.9435" height="29.9376" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.70904"/>
<feGaussianBlur stdDeviation="2.35452"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.278825 0 0 0 0 0.0109642 0 0 0 0 0.0109642 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1028_8037"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1028_8037" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1028_8037" x1="63.4916" y1="42.2998" x2="63.4916" y2="95.0151" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F1FCFF"/>
</linearGradient>
<linearGradient id="paint1_linear_1028_8037" x1="41.7408" y1="63.0893" x2="46.8042" y2="109.933" gradientUnits="userSpaceOnUse">
<stop stop-color="#D9D9D9"/>
<stop offset="1" stop-color="#D9D9D9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1028_8037" x1="9.48745" y1="18.156" x2="68.0381" y2="47.1114" gradientUnits="userSpaceOnUse">
<stop stop-color="#147BF3"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint3_linear_1028_8037" x1="9.48745" y1="18.156" x2="155.456" y2="30.2493" gradientUnits="userSpaceOnUse">
<stop stop-color="#1448F3"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint4_linear_1028_8037" x1="8.48803" y1="42.2625" x2="20.0851" y2="169.26" gradientUnits="userSpaceOnUse">
<stop stop-color="#0AE4EE" stop-opacity="0"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint5_linear_1028_8037" x1="17.6131" y1="1.37931" x2="20.9592" y2="119.058" gradientUnits="userSpaceOnUse">
<stop stop-color="#154BF4" stop-opacity="0"/>
<stop offset="0.349459" stop-color="#154BF4" stop-opacity="0.5"/>
<stop offset="0.637743" stop-color="#154BF4" stop-opacity="0.4"/>
<stop offset="1" stop-color="#0FE0F1"/>
</linearGradient>
<linearGradient id="paint6_linear_1028_8037" x1="-12.851" y1="25.8938" x2="41.3987" y2="146.424" gradientUnits="userSpaceOnUse">
<stop stop-color="#001AFF" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint7_linear_1028_8037" x1="-1.36767" y1="17.2896" x2="30.0832" y2="28.8421" gradientUnits="userSpaceOnUse">
<stop stop-color="#001AFF" stop-opacity="0"/>
<stop offset="0.612337" stop-color="#35ADEE"/>
<stop offset="1" stop-color="#00F0FF"/>
</linearGradient>
<linearGradient id="paint8_linear_1028_8037" x1="-1.36767" y1="18.8057" x2="29.6209" y2="28.8948" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint9_linear_1028_8037" x1="3.83396" y1="0.144349" x2="41.476" y2="13.174" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint10_linear_1028_8037" x1="-1.36767" y1="18.2718" x2="29.4457" y2="28.597" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint11_linear_1028_8037" x1="2.60628" y1="0.170461" x2="41.5164" y2="10.7906" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint12_linear_1028_8037" x1="2.60628" y1="0.170461" x2="41.5164" y2="10.7906" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint13_linear_1028_8037" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint14_linear_1028_8037" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.279251" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint15_linear_1028_8037" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint16_linear_1028_8037" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint17_linear_1028_8037" x1="2.60628" y1="0.112753" x2="38.3322" y2="14.8545" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint18_linear_1028_8037" x1="-1.36767" y1="23.2664" x2="30.6859" y2="31.7015" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint19_linear_1028_8037" x1="-1.36767" y1="25.8938" x2="31.0908" y2="33.5687" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint20_linear_1028_8037" x1="-1.36767" y1="25.8938" x2="31.0908" y2="33.5687" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint21_linear_1028_8037" x1="-1.36767" y1="21.356" x2="30.3025" y2="30.4357" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint22_linear_1028_8037" x1="-1.36767" y1="25.8938" x2="31.0908" y2="33.5687" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint23_linear_1028_8037" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint24_linear_1028_8037" x1="0.109165" y1="0.106343" x2="1.85975" y2="0.138423" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint25_linear_1028_8037" x1="102.512" y1="92.3867" x2="102.512" y2="128.149" gradientUnits="userSpaceOnUse">
<stop stop-color="#3F5173"/>
<stop offset="1" stop-color="#AFC0E1"/>
</linearGradient>
<linearGradient id="paint26_linear_1028_8037" x1="102.512" y1="92.3867" x2="102.512" y2="128.149" gradientUnits="userSpaceOnUse">
<stop stop-color="#3A4C6F"/>
<stop offset="1" stop-color="#96A9CD"/>
</linearGradient>
<linearGradient id="paint27_linear_1028_8037" x1="88.4469" y1="100.888" x2="108.782" y2="108.673" gradientUnits="userSpaceOnUse">
<stop stop-color="#55688D"/>
<stop offset="1" stop-color="#4C628C"/>
</linearGradient>
<linearGradient id="paint28_linear_1028_8037" x1="107.27" y1="91.2779" x2="116.61" y2="115.26" gradientUnits="userSpaceOnUse">
<stop stop-color="#36486B"/>
<stop offset="1" stop-color="#677A9F"/>
</linearGradient>
</defs>
</svg>
