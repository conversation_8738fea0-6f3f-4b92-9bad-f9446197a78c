import { CSSProperties } from 'react';

import clsx from 'clsx';

import searchEmptyPageDark from '../assets/searchEmpty-dark.svg';
import searchEmptyPageLight from '../assets/searchEmpty-light.svg';
import searchEmptyDark from '../assets/searchEmpty-s-dark.svg';
import searchEmptyLight from '../assets/searchEmpty-s-light.svg';
import css from './Index.module.less';

interface Props {
  style?: CSSProperties;
  theme?: 'light' | 'dark';
  type?: 'page' | 'module';
  imgBottom?: React.ReactNode;
  searchText?: string;
}

export const SearchEmpty = (props: Props) => {
  const {
    style,
    theme = 'light',
    type = 'page',
    imgBottom,
    searchText = '暂无相关搜索结果',
  } = props;
  const isDark = theme !== 'light';
  const isPage = type === 'page';
  const pageImg = isDark ? searchEmptyPageDark : searchEmptyPageLight;
  const moduleEmptyImg = isDark ? searchEmptyDark : searchEmptyLight;
  const img = type === 'page' ? pageImg : moduleEmptyImg;
  const backgroundImage = isDark
    ? 'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/notFoundBackgroud-dark.svg'
    : 'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/notFoundBackgroud.svg';
  return (
    <div className={clsx(css['container'], isDark && css['dark'])} style={style}>
      <div
        className={clsx(css['inner'], isPage ? css['page'] : css['module'])}
        style={{
          backgroundImage: isPage ? `url(${backgroundImage})` : undefined,
        }}
      >
        <img src={img} />
        {imgBottom ? (
          imgBottom
        ) : isPage ? (
          <h3 className={css['title']}>{searchText}</h3>
        ) : (
          <p className={css['desc']}>{searchText}</p>
        )}
      </div>
    </div>
  );
};
