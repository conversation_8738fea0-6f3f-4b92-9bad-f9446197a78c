@import '../styles/utils.less';

.imgViewer {
  transition: opacity 0.5s ease;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background: rgba(15, 25, 29, 1);
  position: fixed;
  z-index: 1000;
  display: flex;
  flex-direction: column;

  .header {
    width: 100%;
    height: 98px;
    display: flex;
    justify-content: space-between;
    font-size: 22px;
    font-weight: 500;
    line-height: 34px;
    color: #fff;
    padding: 36px 64px 24px 64px;
    position: relative;

    .leftHeader {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .identitor {
      display: none;
      margin-right: 16px;
    }

    .userMessage {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      flex: 1;
      color: #fff;
    }

    .closeBtn {
      cursor: pointer;
    }
  }

  .header::after {
    content: '';
    position: absolute;
    height: 1px;
    border-radius: 2px;
    opacity: 0.2;
    background: #d6e7ff;
    left: 64px;
    right: 64px;
    bottom: 0px;
  }

  .body {
    width: 100%;
    height: calc(100vh - 99px);
    display: flex;
    // gap: 128px;
    justify-content: space-between;

    .previewContainer {
      flex: 1 1 auto;
      height: 100%;
      overflow: hidden;

      .previewImageContainer {
        height: calc(100% - 72px);
        width: 100%;
        // display: flex;
        // align-items: center;
        // justify-content: center;

        .previewImg {
          // will-change: transform;
          transform-origin: center;

          &:hover {
            cursor: pointer;
          }
        }
      }

      .preImg,
      .nextImg {
        display: none;
        position: absolute;
        top: 50%;
        transform: translateY(-16px);
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        border: 1px solid #e3e6ea;
        background-color: rgba(255, 255, 255, 0.8);
        cursor: pointer;

        &.disable-indicator {
          display: none;
        }
      }

      .preImg {
        left: 24px;
      }

      .nextImg {
        transform: rotate(180deg) translateY(16px);
        right: 24px;
      }
    }

    .waterfall {
      flex: 0 0 auto;
      height: calc(100vh - 24px);
      padding: 0 12px 0 64px;
      margin-right: 42px;
      overflow: scroll;
      .common-scrollbar();
    }

    .imageContainer {
      break-inside: avoid;
      width: 100%;
      column-count: 2;
      column-gap: 8px;

      .imgItemContainer {
        padding: 4px;
        width: 188px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 8px;
        position: relative;
        border-radius: 8px;
        height: fit-content;

        &:hover {
          cursor: pointer;
        }

        &::after {
          position: absolute;
          top: 4px;
          left: 4px;
          border-radius: 8px;
          background: none;
          width: calc(100% - 8px);
          height: calc(100% - 8px);
          content: ' ';
          transition: background 0.3s ease;
          pointer-events: none;
        }

        &:hover::after {
          background: rgba(0, 0, 0, 0.3);
        }
      }

      .imageItemBoxActive {
        background: #326bfb;
      }

      .imageItemBox {
        width: 100%;
        border-radius: 8px;
      }
    }
  }
}

.slider {
  width: 103px;
  margin: 10px 0px !important;
}

.operationSelect {
  width: 85px;
}

.selectIcon {
  color: #666f8d !important;
  font-size: 14px;
}

@media screen and (max-width: 499px) {
  .imgViewer .body .imageContainer {
    column-count: 1;
  }
}

@media screen and (max-width: 499px) {
  .imgViewer {
    .header {
      padding: 36px 24px 24px 24px;

      .userMessage {
        visibility: hidden;
      }

      .identitor {
        display: flex;
      }

      &::after {
        display: none;
      }
    }

    .body {
      .waterfall {
        display: none;
      }

      .previewContainer {
        .preImg,
        .nextImg {
          display: flex;
        }
      }
    }
  }
}

.operationArea {
  width: 100%;
  height: 72px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  position: absolute;
  bottom: 20px;
  padding-right: 805px;

  .operationBar {
    width: 220px;
    height: 50px;
    border: 1px solid transparent;
    border-radius: 8px;
    position: relative;
    background: #f5f7f8;
    display: flex;
    align-items: center;
    padding: 12px;
    justify-content: space-between;

    .operationSliderContainer {
      display: flex;
      gap: 6px;
      align-items: center;

      .operationSliderAdd,
      .operationSliderSub {
        cursor: pointer;
      }
    }

    .operationDownload {
      cursor: pointer;
    }

    .operationBarLeft {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
}

.imgItemContainer {
  position: relative;

  &:hover {
    cursor: pointer;
  }

  &::after {
    position: absolute;
    top: 4px;
    left: 4px;
    border-radius: 8px;
    background: none;
    width: calc(100% - 8px);
    height: calc(100% - 8px);
    content: ' ';
    transition: background 0.3s ease;
    pointer-events: none;
  }

  &:hover::after {
    background: rgba(0, 0, 0, 0.3);
  }
}
