import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';

import { downloadImg } from '@ks-kwaipilot/utils';
import { Slider } from 'antd';
import clsx from 'clsx';

import { Icon } from '../Icon';
import addIcon from '../assets/add.svg';
import downloadIcon from '../assets/download.svg';
import ImgBackupDark from '../assets/img-backup-dark.png';
import subIcon from '../assets/sub.svg';
import css from './index.module.less';
import './slider.global.less';

export interface ImgViewerProps {
  visible: boolean;
  getContainer?: () => HTMLElement;
  imgList?: string[];
  /** 缩放敏感度 */
  zoomSensitivity?: number;
  close: () => void;
  userMessage?: string;
  currentIndex: number;
}

const imageMaxHeight = 600;
const imageMaxWidth = 800;
const maxScaleRate = 4;

export const ImgViewer: React.FC<ImgViewerProps> = (props) => {
  const {
    visible,
    getContainer,
    imgList = [],
    zoomSensitivity = 0.05,
    close,
    userMessage = '',
    currentIndex,
  } = props;

  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [currentImageIndex, setCurrentImageIndex] = useState(currentIndex);
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const isDragging = useRef(false);
  const lastPosition = useRef({ x: 0, y: 0 });
  const imageRef = useRef<HTMLImageElement>();
  const imageBoundsRef = useRef({ width: 0, height: 0 });
  const backupImageRef = useRef<HTMLImageElement>();

  // 绘制画布
  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 保存当前状态
    ctx.save();

    // 移动到画布中心
    ctx.translate(canvas.width / 2, canvas.height / 2);

    // 应用缩放和平移
    ctx.scale(scale, scale);
    ctx.translate(position.x / scale, position.y / scale);

    // 绘制图片
    const imageToDraw = imageRef.current || backupImageRef.current;
    if (imageToDraw) {
      ctx.drawImage(
        imageToDraw,
        -imageToDraw.width / 2,
        -imageToDraw.height / 2,
        imageToDraw.width,
        imageToDraw.height,
      );
    }

    // 恢复状态
    ctx.restore();
  }, [position.x, position.y, scale]);

  // 限制图片移动范围，确保两边或四边同时溢出
  const constrainPosition = useCallback(
    (pos: { x: number; y: number }) => {
      if (!canvasRef.current || (!imageRef.current && !backupImageRef.current)) return pos;

      const canvasWidth = canvasRef.current.width;
      const canvasHeight = canvasRef.current.height;
      const currentImage = imageRef.current || backupImageRef.current;
      const scaledImageWidth = currentImage!.width * scale;
      const scaledImageHeight = currentImage!.height * scale;

      let newX = pos.x;
      let newY = pos.y;

      // 如果缩放后的图片尺寸小于画布，则居中显示
      if (scaledImageWidth <= canvasWidth) {
        newX = 0;
      } else {
        // 限制水平方向的移动范围，确保两边同时溢出
        const maxX = (scaledImageWidth - canvasWidth) / 2;
        newX = Math.max(-maxX, Math.min(maxX, newX));
      }

      if (scaledImageHeight <= canvasHeight) {
        newY = 0;
      } else {
        // 限制垂直方向的移动范围，确保上下同时溢出
        const maxY = (scaledImageHeight - canvasHeight) / 2;
        newY = Math.max(-maxY, Math.min(maxY, newY));
      }

      return { x: newX, y: newY };
    },
    [scale],
  );

  const onDownloadImg = async () => {
    if (imgList[currentImageIndex]) {
      downloadImg(imgList[currentImageIndex]);
    }
  };

  const formatterSliderValue = (value?: number) => {
    if (value) {
      const v = String(value * 100)
        .split('.')
        .shift();
      return `${v}%`;
    }
  };

  const handleChangeScale = useCallback(
    (type: 'add' | 'sub') => {
      return () => {
        const step = 0.1;
        const delta = type === 'add' ? step : -step;
        const newScale = Math.min(Math.max(scale + delta, 0.1), maxScaleRate);
        setScale(newScale);
        setPosition(constrainPosition(position));
      };
    },
    [scale, position, constrainPosition],
  );

  useEffect(() => {
    // 加载备用图片
    const backupImg = new Image();
    backupImg.src = ImgBackupDark;
    backupImg.onload = () => {
      backupImageRef.current = backupImg;
    };

    // 加载主图片
    const img = new Image();

    if (imgList[currentImageIndex]) {
      img.src = imgList[currentImageIndex];
    }

    img.onload = () => {
      const widthRatio = img.naturalWidth / imageMaxWidth;
      const heightRatio = img.naturalHeight / imageMaxHeight;

      if (widthRatio > 1 || heightRatio > 1) {
        const newScale = 1 / Math.max(widthRatio, heightRatio);
        setScale(newScale);
      } else {
        setScale(1);
      }
      setPosition({ x: 0, y: 0 });

      // 调整canvas大小
      if (canvasRef.current) {
        canvasRef.current.width = containerRef.current?.clientWidth || 800;
        canvasRef.current.height = containerRef.current?.clientHeight || 600;
      }

      imageRef.current = img;
      imageBoundsRef.current = {
        width: img.width,
        height: img.height,
      };
      drawCanvas();
    };
    img.onerror = () => {
      if (backupImageRef.current) {
        imageRef.current = undefined;
        imageBoundsRef.current = {
          width: backupImageRef.current.width,
          height: backupImageRef.current.height,
        };
        drawCanvas();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentImageIndex, imgList]);

  useEffect(() => {
    drawCanvas();
  }, [drawCanvas]);

  /** 监听事件 */
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleWheel = (e: WheelEvent) => {
      // 检查鼠标是否在canvas容器内
      const canvasContainer = container.querySelector('#img-viewer-canvas');
      if (!canvasContainer) return;

      const rect = canvasContainer.getBoundingClientRect();
      const isInCanvasContainer =
        e.clientX >= rect.left &&
        e.clientX <= rect.right &&
        e.clientY >= rect.top &&
        e.clientY <= rect.bottom;

      if (!isInCanvasContainer) return;

      e.preventDefault();

      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;

      const delta = -Math.sign(e.deltaY);
      const oldScale = scale;
      const newScale = Math.min(Math.max(scale + delta * zoomSensitivity, 0.1), maxScaleRate);

      if (newScale !== scale) {
        const scaleRatio = newScale / oldScale;

        // 以鼠标位置为中心进行缩放
        const newX = mouseX - (mouseX - position.x) * scaleRatio;
        const newY = mouseY - (mouseY - position.y) * scaleRatio;

        setScale(newScale);
        // 应用位置约束
        setPosition(constrainPosition({ x: newX, y: newY }));
      }
    };

    const handleMouseDown = (e: MouseEvent) => {
      if (e.button === 0) {
        isDragging.current = true;
        lastPosition.current = { x: e.clientX, y: e.clientY };
      }
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging.current) {
        const deltaX = e.clientX - lastPosition.current.x;
        const deltaY = e.clientY - lastPosition.current.y;

        const newPosition = {
          x: position.x + deltaX,
          y: position.y + deltaY,
        };

        // 应用位置约束
        setPosition(constrainPosition(newPosition));
        lastPosition.current = { x: e.clientX, y: e.clientY };
      }
    };

    const handleMouseUp = () => {
      isDragging.current = false;
    };

    container.addEventListener('wheel', handleWheel, { passive: false });
    container.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      container.removeEventListener('wheel', handleWheel);
      container.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [scale, zoomSensitivity, position, constrainPosition]);

  const render = () => {
    return visible ? (
      <div className="fixed left-0 top-0 z-[9999] flex h-[100vh] w-[100vw] flex-col bg-black">
        <div className="relative flex h-[98px] w-full items-center gap-2 px-16 pb-6 pt-9">
          <div className="flex-auto truncate text-[22px] font-medium leading-[34px] text-white">
            {userMessage}
          </div>
          <Icon
            name="kwaipilot_system_close"
            className="flex-shrink-0 cursor-pointer !text-white"
            size={24}
            onClick={close}
          />
          <div className="absolute bottom-0 left-16 h-[1px] w-[calc(100%-128px)] bg-[#D6E7FF] opacity-20"></div>
        </div>
        <div
          className="flex flex-auto overflow-hidden"
          id="img-viewer-container"
          ref={containerRef}
        >
          <div className="flex flex-auto flex-col overflow-hidden">
            <div
              className="flex flex-auto items-center justify-center overflow-hidden"
              id="img-viewer-canvas"
            >
              <canvas
                ref={canvasRef}
                style={{
                  cursor: 'move',
                }}
              />
            </div>
            <div className={css.operationArea}>
              <div className={css.operationBar}>
                <div className={css.operationBarLeft}>
                  <div className={css.operationSliderContainer}>
                    <img
                      src={subIcon}
                      alt=""
                      className={css.operationSliderSub}
                      onClick={handleChangeScale('sub')}
                    />
                    <Slider
                      className={clsx(css.slider, 'img-view-slider')}
                      step={0.01}
                      min={0.1}
                      max={4}
                      value={scale}
                      onChange={(value) => setScale(value)}
                      tooltip={{
                        formatter: formatterSliderValue,
                      }}
                    ></Slider>
                    <img
                      src={addIcon}
                      alt=""
                      className={css.operationSliderAdd}
                      onClick={handleChangeScale('add')}
                    />
                  </div>
                </div>
                <img
                  src={downloadIcon}
                  alt=""
                  className={css.operationDownload}
                  onClick={onDownloadImg}
                />
              </div>
            </div>
          </div>
          <div className="mb-6 w-[805px] overflow-y-auto px-[64px]">
            <div className="columns-2 gap-2">
              {imgList?.map((image: string, index: number) => (
                <div
                  key={`image-${image}-${index}`}
                  className={`relative mb-2 flex break-inside-avoid items-center justify-center rounded-lg p-1 ${currentImageIndex === index ? 'bg-brand' : ''} ${css.imgItemContainer}`}
                  style={{
                    transform: 'translateZ(0)', // 启用硬件加速
                    backfaceVisibility: 'hidden', // 防止3D渲染问题
                    WebkitBackfaceVisibility: 'hidden',
                  }}
                >
                  <img
                    src={image}
                    className="h-auto w-full cursor-pointer rounded-lg object-cover"
                    onClick={() => setCurrentImageIndex(index)}
                    style={{
                      transform: 'translateZ(0)', // 启用硬件加速
                      backfaceVisibility: 'hidden', // 防止3D渲染问题
                      WebkitBackfaceVisibility: 'hidden',
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    ) : null;
  };
  return getContainer ? ReactDOM.createPortal(render(), getContainer()) : render();
};
