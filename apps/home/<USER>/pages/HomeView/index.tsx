import { useEffect, useRef, useState } from 'react';

import clsx from 'clsx';

import { NavBar } from '@/components/NavBar';
import VideoIntroduction from '@/components/VideoIntroduction';
import { ask } from '@/utils/ask';
import { collectFMP } from '@/utils/weblogger';

import css from './index.module.less';

export function HomeView() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const hoverRef = useRef<boolean>(true);
  const [query, setQuery] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  function setHover(bo: boolean) {
    hoverRef.current = bo;
  }
  async function handleAsk() {
    await ask(query);
    setQuery('');
  }
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    if (event.key === 'Enter' && !isComposing) {
      handleAsk();
    }
  };
  function handleNext() {
    const screenHeight = window.innerHeight; // 获取屏幕可视区域高度
    window.scrollTo({
      top: screenHeight,
      behavior: 'smooth',
    }); // 将滚动条滚动到距离顶部一屏幕的高度
  }
  function focusInput() {
    if (inputRef.current) inputRef.current.focus();
  }
  useEffect(() => {
    collectFMP();
    if (videoRef.current) {
      videoRef.current.play();
    }
    function handleFocus(ev: KeyboardEvent) {
      if (ev.key === '/' && inputRef.current && hoverRef.current) {
        setTimeout(() => {
          if (inputRef.current) inputRef.current.focus();
        }, 0);
      }
    }
    window.addEventListener('keydown', handleFocus);
    return () => window.addEventListener('keydown', handleFocus);
  }, []);
  return (
    <div
      className={clsx(css['home-view'])}
      onMouseMove={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      <NavBar></NavBar>
      <div className={clsx(css['home-view-bg-container'])}>
        <video className={clsx(css['home-view-bg'])} autoPlay muted loop ref={videoRef}>
          <source src="https://cdnfile.corp.kuaishou.com/kc/files/a/kdev-workbench/kinsight-prod/dist/web/kwaipilot/assets/KwaipilotHome/Star.mp4" />
        </video>
      </div>
      <div className={clsx(css['home-view-mask'])}></div>
      <div className={clsx(css['home-title'])}></div>
      <div className={clsx(css['home-sub-title'])}>最懂快手研发的AI工具</div>
      <div className={clsx(css['home-input-border'])} onClick={focusInput}>
        <div className={clsx(css['home-input-card'])}>
          <input
            id="home-input"
            ref={inputRef}
            value={query}
            className={clsx(css['home-input'])}
            placeholder=" "
            autoComplete="off"
            onChange={(ev) => setQuery(ev.target.value)}
            onKeyDown={handleKeyDown}
            onCompositionStart={() => setIsComposing(true)}
            onCompositionEnd={() => setIsComposing(false)}
          />
          <div className={clsx(css['home-input--placeholder'])}>
            试试和我对话...
            <span className={clsx(css['home-input--icon'])}>/</span>
          </div>
          <div className={clsx(css['home-input-btn-border'])}>
            <div className={clsx('btn-card', css['home-input-btn'])} onClick={handleAsk}>
              Start now
            </div>
          </div>
        </div>
      </div>
      <div className={clsx(css['home-video-introduction'])}>
        <VideoIntroduction />
      </div>
      <div onClick={handleNext} className={clsx(css['home-next'], 'btn-pos btn-down')}>
        快速了解
        <div className="btn-down--after"></div>
      </div>
    </div>
  );
}
